<?php if($PROGRAM->kate<PERSON><PERSON>_Pengajian=='T'): ?>
    <?php if(!empty($syarat_stam_0[0])): ?>                                         
        <li style="padding-left: .3em; margin-bottom:8px;">                           
            <?php $__currentLoopData = $syarat_stam_0; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status_stam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>                                       
            Mendapat sekurang-kurangnya 
            <b>
                <?php if($status_stam->tahap_STAM=='1'): ?> MUMTAZ
                <?php elseif($status_stam->tahap_STAM=='2'): ?> JAYYID JIDDAN
                <?php elseif($status_stam->tahap_STAM=='3'): ?> JAYYID
                <?php elseif($status_stam->tahap_STAM=='4'): ?> MAQBUL
                <?php elseif($status_stam->tahap_STAM=='5'): ?> RASIB
                <?php endif; ?>
            </b> 
            pada peringkat <b>STAM</b>.                                                                   
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </li>
        
    <?php endif; ?>
    <?php else: ?>
    <?php if(!empty($syarat_stpm_matrik_0[0])): ?>                                         
        <li style="padding-left: .3em; margin-bottom:8px;">                           
            <?php $__currentLoopData = $syarat_stpm_matrik_0; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status_pngk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 									
            Mendapat sekurang-kurangnya <b>PNGK <?php echo e($status_pngk->PNGK_STPM); ?> </b> pada peringkat 																				                                               
                <?php if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S'): ?> <b>STPM</b>. 
                <?php elseif($PROGRAM->kategori_Pengajian=='T'): ?> <b>STAM</b>. 
                <?php elseif($PROGRAM->kategori_Pengajian=='N'): ?> <b>Matrikulasi / Asasi</b>. 
                <?php elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J'): ?> <b>Matrikulasi</b>. 
                <?php else: ?> <b>Asasi</b>. 
                <?php endif; ?>																																											
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </li>
    <?php endif; ?>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat_stpm/syarat_pngk_0.blade.php ENDPATH**/ ?>