

<?php $__currentLoopData = $j<PERSON><PERSON><PERSON><PERSON>ian; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $jpengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

    <?php
    $syaratam_am_spm  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    $syaratam_am  = DB::connection('emas')->table('syarat_am_pengajian_stpm')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    $syaratam_am_lain  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    $syaratam_am_muet  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    $syaratam_diploma  = DB::connection('emas')->table('syarat_am_diploma')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();

    $syaratam_am_lain_matrik  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->where('KATEGORI','matrik')->get();
    $syaratam_am_lain_stpm  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->where('KATEGORI','stpm')->get();
    $syaratam_am_lain_stam  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->where('KATEGORI','stam')->get();
    $syaratam_am_lain_diploma  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->where('KATEGORI','diploma')->get();
    ?>



    <div class="pl-2 pb-3" style="line-height:35px; float:left; width: 90%; margin-top:-1px; margin-bottom: -15px;">

        <?php if($jpengajian->KOD_PENGAJIAN=='SM1' && ($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='L' || $PROGRAM->kategori_Pengajian=='U' || $PROGRAM->kategori_Pengajian=='N' || $PROGRAM->kategori_Pengajian=='K' || $PROGRAM->kategori_Pengajian=='J' || $PROGRAM->kategori_Pengajian=='M' || $PROGRAM->kategori_Pengajian=='V') ||
		$jpengajian->KOD_PENGAJIAN=='SM2' && ($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') ||
		$jpengajian->KOD_PENGAJIAN=='SM3' && $PROGRAM->kategori_Pengajian=='T'): ?>
		<?php if($loop->index): ?>
            <div><u><b>SPM</b></u></div>
            <div>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</div>
            <ol style="padding-left: 1.5em;">
                <?php $__currentLoopData = $syaratam_am_spm; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam_spm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($syaratam_spm->KODSUBJEK!='F'): ?>
                        <li style="padding-left: .3em;">
                            Gred <b><?php echo e($syaratam_spm->MINGRED); ?></b> bagi matapelajaran
                            <?php $__currentLoopData = $subjek; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spm_subjek): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($spm_subjek->kodsubjekspm==$syaratam_spm->KODSUBJEK): ?>
                                    <b><?php echo e($spm_subjek->ketsubjekspm); ?><?php if($syaratam_spm->KERTAS_JULAI=='T' && $syaratam_spm->KODSUBJEK=='1103'): ?>. <?php endif; ?>
                                        <?php if($syaratam_spm->KERTAS_JULAI=='Y' && $syaratam_spm->KODSUBJEK=='1103'): ?> / BAHASA MELAYU (KERTAS JULAI). <?php endif; ?>
                                    </b>
                                    <?php if($syaratam_spm->KODSUBJEK=='1249'): ?> mulai tahun 2013. <?php endif; ?>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </li>
                    <?php endif; ?>

                    <?php
                    if($syaratam_spm->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
                    elseif($syaratam_spm->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
                    elseif($syaratam_spm->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
                    elseif($syaratam_spm->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
                    elseif($syaratam_spm->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
                    ?>

                    <?php if($syaratam_spm->KODSUBJEK=='F'): ?>
                        <li style="padding-left: .3em;"><b><?php echo e($read); ?> (<?php echo e($syaratam_spm->JUMLAH_MIN_SUBJEK); ?>)</b> Gred <b><?php echo e($syaratam_spm->MINGRED); ?></b> dalam mana-mana matapelajaran yang belum dikira.</li>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ol>

            <div class="text-center"><b>DAN</b></div>
        <?php endif; ?>
        <?php endif; ?>

        <div>
            <u><b>
                <?php if($jpengajian->KOD_PENGAJIAN=='SM1' && ($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='L' || $PROGRAM->kategori_Pengajian=='U' || $PROGRAM->kategori_Pengajian=='N' || $PROGRAM->kategori_Pengajian=='K' || $PROGRAM->kategori_Pengajian=='J' || $PROGRAM->kategori_Pengajian=='M' || $PROGRAM->kategori_Pengajian=='V')): ?> MATRIK / ASASI <?php endif; ?>
                <?php if($jpengajian->KOD_PENGAJIAN=='SM2' && ($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S')): ?> STPM <?php endif; ?>
                <?php if($jpengajian->KOD_PENGAJIAN=='SM3' && $PROGRAM->kategori_Pengajian=='T'): ?> STAM <?php endif; ?>
            </b></u>
        </div>

        <?php if($jpengajian->KOD_PENGAJIAN=='SM1' && ($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='L' || $PROGRAM->kategori_Pengajian=='U' || $PROGRAM->kategori_Pengajian=='N' || $PROGRAM->kategori_Pengajian=='K' || $PROGRAM->kategori_Pengajian=='J' || $PROGRAM->kategori_Pengajian=='M' || $PROGRAM->kategori_Pengajian=='V')): ?>
            <?php if($loop->index): ?>
            <div>Lulus Matrikulasi KPM / Asasi Sains UM / Asasi UKM / Asasi UiTM dengan mendapat sekurang-kurangnya :-</div>
            <ol style="padding-left: 1.5em;">
                <?php $__currentLoopData = $syaratam_am_lain; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam_lain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li style="padding-left: .3em;">Purata Nilai Gred Kumulatif (PNGK)
                    <b><?php echo e($syaratam_lain->PNGK); ?></b>.
                </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($syaratam->KODSUBJEK=='F'): ?>
                        <li style="padding-left: .3em;"><b><?php echo e($read); ?> (<?php echo e($syaratam->JUMLAH_MIN_SUBJEK); ?>)</b> Gred <b><?php echo e($syaratam->MINGRED); ?></b> dalam mana-mana matapelajaran yang belum dikira.</li>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ol>
            <?php endif; ?>
        <?php endif; ?>


        <?php if($jpengajian->KOD_PENGAJIAN=='SM2' && ($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S')): ?>
        <?php if($loop->index): ?>
        <div>Lulus Peperiksaan <b>Sijil Tinggi Persekolahan Malaysia (STPM)</b> dengan mendapat sekurang-kurangnya :-</div>
        <ol style="padding-left: 1.5em;">
            <?php $__currentLoopData = $syaratam_am_lain; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam_lain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li style="padding-left: .3em;">Purata Nilai Gred Kumulatif (PNGK)
                <b><?php echo e($syaratam_lain->PNGK); ?></b>.
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($syaratam->KODSUBJEK!='F'): ?>
                    <li style="padding-left: .3em;">
                        Gred <b><?php echo e($syaratam->MINGRED); ?></b> bagi matapelajaran
                        <?php $__currentLoopData = $subjek_stpm; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stpm_subjek): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($stpm_subjek->kodsubjekstpm==$syaratam->KODSUBJEK): ?>
                                <b><?php echo e($stpm_subjek->ketsubjekstpm); ?></b>.
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </li>
                <?php endif; ?>
                <?php
                if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
                elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
                elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
                elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
                elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
                ?>

                <?php if($syaratam->KODSUBJEK=='F'): ?>
                    <li style="padding-left: .3em;"><b><?php echo e($read); ?> (<?php echo e($syaratam->JUMLAH_MIN_SUBJEK); ?>)</b> Gred <b><?php echo e($syaratam->MINGRED); ?></b> dalam mana-mana matapelajaran yang belum dikira.</li>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ol>
        <?php endif; ?>
    <?php endif; ?>

    <?php if($jpengajian->KOD_PENGAJIAN=='SM3' && $PROGRAM->kategori_Pengajian=='T'): ?>
    <?php if($loop->index): ?>
        <div>Mendapat sekurang-kurangnya :-</div>
        <ol style="padding-left: 1.5em;">
            <?php $__currentLoopData = $syaratam_am_lain; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam_lain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li style="padding-left: .3em;">Pangkat

                <?php $__currentLoopData = $codeset_stam; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $codesetstam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($codesetstam->kodthpstam==$syaratam_lain->TAHAP_STAM): ?>
                        <b><?php echo e($codesetstam->ketthpstam); ?></b> dalam Peperiksaan Sijil Tinggi Agama Malaysia (STAM).
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($syaratam->KODSUBJEK=='F'): ?>
                <li style="padding-left: .3em;"><b><?php echo e($read); ?> (<?php echo e($syaratam->JUMLAH_MIN_SUBJEK); ?>)</b> Gred <b><?php echo e($syaratam->MINGRED); ?></b> dalam mana-mana matapelajaran yang belum dikira.</li>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ol>
    <?php endif; ?>
    <?php endif; ?>

    <?php if($jpengajian->KOD_PENGAJIAN=='SM1' && ($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='L' || $PROGRAM->kategori_Pengajian=='U' || $PROGRAM->kategori_Pengajian=='N' || $PROGRAM->kategori_Pengajian=='K' || $PROGRAM->kategori_Pengajian=='J' || $PROGRAM->kategori_Pengajian=='M' || $PROGRAM->kategori_Pengajian=='V') ||
    ($jpengajian->KOD_PENGAJIAN=='SM2' && ($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S')) ||
    ($jpengajian->KOD_PENGAJIAN=='SM3' && $PROGRAM->kategori_Pengajian=='T') ||
    ($jpengajian->KOD_PENGAJIAN=='SM4' && $PROGRAM->kategori_Pengajian=='G') ||
    ($jpengajian->KOD_PENGAJIAN=='SM5' && $PROGRAM->kategori_Pengajian=='E') ||
    ($jpengajian->KOD_PENGAJIAN=='SM6' && $PROGRAM->kategori_Pengajian=='E')): ?>

    <div class="text-center"><b>DAN</b></div>
        <div> <u><b>MUET</b></u></div>
        <div>Mendapat sekurang-kurangnya
            <?php $__currentLoopData = $syaratam_am_lain; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam_lain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php $__currentLoopData = $codeset_muet2; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $muet2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($syaratam_lain->MUET1_BAND==$muet2->kodthpmuet): ?>
                        <b style="text-transform: capitalize;"><?php echo e($muet2->ketthpmuet); ?></b> dalam <b>Malaysian University English Test (MUET)</b> untuk peperiksaan yang bermula Sesi 1 tahun <?php echo e($syaratam_lain->TAHUN1_BAND); ?> <b>ATAU</b>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <b>BAND <?php echo e($syaratam_lain->MUET2_BAND); ?></b> untuk peperiksaan sehingga tahun <?php echo e($syaratam_lain->TAHUN2_BAND); ?> <b>mengikut tempoh sah laku pada tarikh permohonan</b>.
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>


    </div>


<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/syarat_am_stpm.blade.php ENDPATH**/ ?>