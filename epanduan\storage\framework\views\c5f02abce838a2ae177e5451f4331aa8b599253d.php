<?php $__currentLoopData = $syaratkhas_f1; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_f1): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <li style="padding-left: .3em; margin-bottom:8px;"> 
        Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_f1->MINGRED); ?></b> dalam mana-mana <b><?php echo e($syarat_khas_f1->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syarat_khas_f1->JUMLAH_MIN_SUBJEK); ?>)</b> 

        <?php if($syarat_khas_f1->SUB_KUMPULAN=='F'): ?> mata pelajaran.
        <?php elseif($syarat_khas_f1->SUB_KUMPULAN=='X'): ?> mata pelajaran yang belum diambil kira
        <?php elseif($syarat_khas_f1->SUB_KUMPULAN=='Y'): ?>  mata pelajaran selain diatas
        <?php endif; ?>

        pada peringkat <b>SPM</b>. 
    </li>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat_stpm/k0/syarat_khas_f1.blade.php ENDPATH**/ ?>