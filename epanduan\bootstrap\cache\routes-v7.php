<?php

/*
|--------------------------------------------------------------------------
| Load The Cached Routes
|--------------------------------------------------------------------------
|
| Here we will decode and unserialize the RouteCollection instance that
| holds all of the route information for an application. This allows
| us to instantaneously load the entire route map into the router.
|
*/

app('router')->setCompiledRoutes(
    array (
  'compiled' => 
  array (
    0 => false,
    1 => 
    array (
      '/sanctum/csrf-cookie' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::KdF4O0GE4fyTURko',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/user' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::yghr6k8MJD6jKrPa',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/clear-all' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::dKFl18phIfiUkrAE',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/optimize' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::YMB6psvgKfEHnE7l',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::ZQNA6uB5TX92n6h0',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/senaraiAgensi' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::VV7GpYzsdzuZ8v7a',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/kategoriCalon' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::V9o1PHpI1sRO8ypb',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/carianNamaProgram' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::AnAc8ysIAM3iLp91',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/carianNamaProgram/carianProgramRawak' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::ZzRBCuTimBZgE476',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/kalkulatorMerit/spm' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::7svEjsyr7DaSqu5i',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/kalkulatorMerit/stpm' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::g5ea6t7iVbjZ6RJL',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/kalkulatorMerit/diploma' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::s7P405VX2Z1iRohd',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/kalkulatorMerit/stam' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::yCjazq6XcihFkvMQ',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
    ),
    2 => 
    array (
      0 => '{^(?|/kategoriCalon/([^/]++)(*:30)|/maklumatAgensi/([^/]++)(*:61)|/carianNamaProgram/([^/]++)/([^/]++)/([^/]++)/([^/]++)(?|(*:125)|(*:133))|/ProgramPengajian/(?|kategoriCalon/([^/]++)(?|(*:188)|(*:196)|/sort/([^/]++)(*:218))|modalSyarat(?|/([^/]++)/([^/]++)(*:259)|diploma/([^/]++)/([^/]++)/([^/]++)(*:301)))|/ailis/s(?|pm/([^/]++)/([^/]++)(*:342)|tpm/([^/]++)/([^/]++)(*:371)))/?$}sDu',
    ),
    3 => 
    array (
      30 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'kategoriCalon',
          ),
          1 => 
          array (
            0 => 'jenprog',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      61 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::u5N9oMBNI8wSrtvf',
          ),
          1 => 
          array (
            0 => 'nama_ipta',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      125 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::ofrgRxaiKWz4et4K',
          ),
          1 => 
          array (
            0 => 'kodipta',
            1 => 'kodprogram',
            2 => 'katag',
            3 => 'jenprog',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      133 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::w2H8zH6nLMbKSx05',
          ),
          1 => 
          array (
            0 => 'kodipta',
            1 => 'kodprogram',
            2 => 'jensetaraf',
            3 => 'jenprog',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      188 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::BT0B2bK5q7L3trUj',
          ),
          1 => 
          array (
            0 => 'kodkatag',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      196 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::OhEsnAxJIj5ne6VP',
          ),
          1 => 
          array (
            0 => 'kod',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      218 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::rXBCgHBPLvD40bKp',
          ),
          1 => 
          array (
            0 => 'kodkatag',
            1 => 'sort_order',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      259 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::MkZcuj5hJd6KVMjU',
          ),
          1 => 
          array (
            0 => 'kodkatag',
            1 => 'kod',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      301 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::YpiM5kjXYwoLxGEi',
          ),
          1 => 
          array (
            0 => 'kodkatag',
            1 => 'kodsetaraf',
            2 => 'kod',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      342 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::przJOyxa2dcyDvyF',
          ),
          1 => 
          array (
            0 => 'kodkatag',
            1 => 'kod',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      371 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::HZprkfpa9Kt705t9',
          ),
          1 => 
          array (
            0 => 'kodkatag',
            1 => 'kod',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => NULL,
          1 => NULL,
          2 => NULL,
          3 => NULL,
          4 => false,
          5 => false,
          6 => 0,
        ),
      ),
    ),
    4 => NULL,
  ),
  'attributes' => 
  array (
    'generated::KdF4O0GE4fyTURko' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'sanctum/csrf-cookie',
      'action' => 
      array (
        'uses' => 'Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show',
        'controller' => 'Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show',
        'namespace' => NULL,
        'prefix' => 'sanctum',
        'where' => 
        array (
        ),
        'middleware' => 
        array (
          0 => 'web',
        ),
        'as' => 'generated::KdF4O0GE4fyTURko',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::yghr6k8MJD6jKrPa' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/user',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'auth:sanctum',
        ),
        'uses' => 'C:32:"Opis\\Closure\\SerializableClosure":289:{@h54tc1FwcXATCl6UdxvBUfwQeMoA0CByt/B3LhbE370=.a:5:{s:3:"use";a:0:{}s:8:"function";s:77:"function (\\Illuminate\\Http\\Request $request) {
    return $request->user();
}";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"000000003cd631c7000000005b3b0bac";}}',
        'namespace' => NULL,
        'prefix' => 'api',
        'where' => 
        array (
        ),
        'as' => 'generated::yghr6k8MJD6jKrPa',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::dKFl18phIfiUkrAE' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'clear-all',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'C:32:"Opis\\Closure\\SerializableClosure":439:{@aMZ9VYvIOayWp4cgvAJq79skMN/6BxpMAL2xobA5yUc=.a:5:{s:3:"use";a:0:{}s:8:"function";s:226:"function() {
    \\Artisan::call(\'route:clear\');
    \\Artisan::call(\'view:clear\');
    \\Artisan::call(\'cache:clear\');
    \\Artisan::call(\'config:clear\');
    
    return "All caches (route, view, cache, config) are cleared!";
}";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"000000003cd631c1000000005b3b0bac";}}',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::dKFl18phIfiUkrAE',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::YMB6psvgKfEHnE7l' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'optimize',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'C:32:"Opis\\Closure\\SerializableClosure":292:{@G9PG6JQS56N6A/s1ysiPSa2x3OaikT+QiVbmsN3Aobo=.a:5:{s:3:"use";a:0:{}s:8:"function";s:80:"function() {
    \\Artisan::call(\'optimize\');
    return "optimize Successful";
}";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"000000003cd631c3000000005b3b0bac";}}',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::YMB6psvgKfEHnE7l',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::ZQNA6uB5TX92n6h0' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '/',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\HalamanUtamaController@index',
        'controller' => 'App\\Http\\Controllers\\HalamanUtamaController@index',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::ZQNA6uB5TX92n6h0',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::VV7GpYzsdzuZ8v7a' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'senaraiAgensi',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\SenaraiAgensiController@index',
        'controller' => 'App\\Http\\Controllers\\SenaraiAgensiController@index',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::VV7GpYzsdzuZ8v7a',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::V9o1PHpI1sRO8ypb' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'kategoriCalon',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\SenaraiKategoriController@index',
        'controller' => 'App\\Http\\Controllers\\SenaraiKategoriController@index',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::V9o1PHpI1sRO8ypb',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'kategoriCalon' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'kategoriCalon/{jenprog}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\SenaraiKategoriController@index',
        'controller' => 'App\\Http\\Controllers\\SenaraiKategoriController@index',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'kategoriCalon',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::u5N9oMBNI8wSrtvf' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'maklumatAgensi/{nama_ipta}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\MaklumatAgensiController@index',
        'controller' => 'App\\Http\\Controllers\\MaklumatAgensiController@index',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::u5N9oMBNI8wSrtvf',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::AnAc8ysIAM3iLp91' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'carianNamaProgram',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\CarianKategoriProgramController@index',
        'controller' => 'App\\Http\\Controllers\\CarianKategoriProgramController@index',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::AnAc8ysIAM3iLp91',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::ZzRBCuTimBZgE476' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'carianNamaProgram/carianProgramRawak',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\CarianKategoriProgramController@carianProgramRawak',
        'controller' => 'App\\Http\\Controllers\\CarianKategoriProgramController@carianProgramRawak',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::ZzRBCuTimBZgE476',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::ofrgRxaiKWz4et4K' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'carianNamaProgram/{kodipta}/{kodprogram}/{katag}/{jenprog}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\CarianKategoriProgramController@paparProgramRawak',
        'controller' => 'App\\Http\\Controllers\\CarianKategoriProgramController@paparProgramRawak',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::ofrgRxaiKWz4et4K',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::w2H8zH6nLMbKSx05' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'carianNamaProgram/{kodipta}/{kodprogram}/{jensetaraf}/{jenprog}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\CarianKategoriProgramController@paparEFGProgramRawak',
        'controller' => 'App\\Http\\Controllers\\CarianKategoriProgramController@paparEFGProgramRawak',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::w2H8zH6nLMbKSx05',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::BT0B2bK5q7L3trUj' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ProgramPengajian/kategoriCalon/{kodkatag}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\ProgramPengajianController@index',
        'controller' => 'App\\Http\\Controllers\\ProgramPengajianController@index',
        'namespace' => NULL,
        'prefix' => '/ProgramPengajian',
        'where' => 
        array (
        ),
        'as' => 'generated::BT0B2bK5q7L3trUj',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::MkZcuj5hJd6KVMjU' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ProgramPengajian/modalSyarat/{kodkatag}/{kod}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\ProgramPengajianController@modal_syarat',
        'controller' => 'App\\Http\\Controllers\\ProgramPengajianController@modal_syarat',
        'namespace' => NULL,
        'prefix' => '/ProgramPengajian',
        'where' => 
        array (
        ),
        'as' => 'generated::MkZcuj5hJd6KVMjU',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::YpiM5kjXYwoLxGEi' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ProgramPengajian/modalSyaratdiploma/{kodkatag}/{kodsetaraf}/{kod}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\ProgramPengajianController@modal_syarat_diploma',
        'controller' => 'App\\Http\\Controllers\\ProgramPengajianController@modal_syarat_diploma',
        'namespace' => NULL,
        'prefix' => '/ProgramPengajian',
        'where' => 
        array (
        ),
        'as' => 'generated::YpiM5kjXYwoLxGEi',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::OhEsnAxJIj5ne6VP' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'ProgramPengajian/kategoriCalon/{kod}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\ProgramPengajianController@carianProgram',
        'controller' => 'App\\Http\\Controllers\\ProgramPengajianController@carianProgram',
        'namespace' => NULL,
        'prefix' => '/ProgramPengajian',
        'where' => 
        array (
        ),
        'as' => 'generated::OhEsnAxJIj5ne6VP',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::rXBCgHBPLvD40bKp' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ProgramPengajian/kategoriCalon/{kodkatag}/sort/{sort_order}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\ProgramPengajianController@index',
        'controller' => 'App\\Http\\Controllers\\ProgramPengajianController@index',
        'namespace' => NULL,
        'prefix' => '/ProgramPengajian',
        'where' => 
        array (
        ),
        'as' => 'generated::rXBCgHBPLvD40bKp',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::7svEjsyr7DaSqu5i' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'kalkulatorMerit/spm',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\KalkulatorMeritController@calcSPM',
        'controller' => 'App\\Http\\Controllers\\KalkulatorMeritController@calcSPM',
        'namespace' => NULL,
        'prefix' => '/kalkulatorMerit',
        'where' => 
        array (
        ),
        'as' => 'generated::7svEjsyr7DaSqu5i',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::g5ea6t7iVbjZ6RJL' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'kalkulatorMerit/stpm',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\KalkulatorMeritController@calcSTPM',
        'controller' => 'App\\Http\\Controllers\\KalkulatorMeritController@calcSTPM',
        'namespace' => NULL,
        'prefix' => '/kalkulatorMerit',
        'where' => 
        array (
        ),
        'as' => 'generated::g5ea6t7iVbjZ6RJL',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::s7P405VX2Z1iRohd' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'kalkulatorMerit/diploma',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\KalkulatorMeritController@calcDIPLOMA',
        'controller' => 'App\\Http\\Controllers\\KalkulatorMeritController@calcDIPLOMA',
        'namespace' => NULL,
        'prefix' => '/kalkulatorMerit',
        'where' => 
        array (
        ),
        'as' => 'generated::s7P405VX2Z1iRohd',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::yCjazq6XcihFkvMQ' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'kalkulatorMerit/stam',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'App\\Http\\Controllers\\KalkulatorMeritController@calcSTAM',
        'controller' => 'App\\Http\\Controllers\\KalkulatorMeritController@calcSTAM',
        'namespace' => NULL,
        'prefix' => '/kalkulatorMerit',
        'where' => 
        array (
        ),
        'as' => 'generated::yCjazq6XcihFkvMQ',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::przJOyxa2dcyDvyF' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ailis/spm/{kodkatag}/{kod}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'C:32:"Opis\\Closure\\SerializableClosure":846:{@LiLfv/VfcuXI2XJQhCS7N5IgsB5kbczTFx9MTyNRIKk=.a:5:{s:3:"use";a:0:{}s:8:"function";s:633:"function (\\Illuminate\\Http\\Request $request, $kodkatag, $kod) {
    try{
	\\session([\'jenprog\' => \'spm\', \'sesi_semasa\' => \'2526\']);

/*$controller = new ProgramPengajianController();
$controller->modal_syarat($request, $kodkatag, $kod);*/
return $response = \\app()->call([\\App\\Http\\Controllers\\ProgramPengajianController::class, \'modal_syarat\'], [
            \'request\' => $request,
            \'kodkatag\' => $kodkatag,
            \'kod\' => $kod
        ]);
} catch (\\Throwable $e) {
return \\response()->json([
            \'data\' => \'Tiada syarat program ditetapkan untuk permohonan di bawah kategori \'.$kodkatag,
        ], 200);
}
}";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"000000003cd631f4000000005b3b0bac";}}',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::przJOyxa2dcyDvyF',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::HZprkfpa9Kt705t9' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'ailis/stpm/{kodkatag}/{kod}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'C:32:"Opis\\Closure\\SerializableClosure":654:{@JJ4G4kQHYvMh/gZFuhbrzf512eyC4aULHLA8pPfEA0I=.a:5:{s:3:"use";a:0:{}s:8:"function";s:441:"function (\\Illuminate\\Http\\Request $request, $kodkatag, $kod) {
try{
	\\session([\'jenprog\' => \'stpm\', \'sesi_semasa\' => \'2526\']);

$controller = new \\App\\Http\\Controllers\\ProgramPengajianController();
return $controller->modal_syarat($request, $kodkatag, $kod);
} catch (\\Throwable $e) {
return \\response()->json([
            \'data\' => \'Tiada syarat program ditetapkan untuk permohonan di bawah kategori \'.$kodkatag,
        ], 200);
}
    
}";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"000000003cd631e9000000005b3b0bac";}}',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'generated::HZprkfpa9Kt705t9',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
  ),
)
);
