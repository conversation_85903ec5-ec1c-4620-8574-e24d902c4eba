
<?php if($PROGRAM->kate<PERSON><PERSON>_Pengajian!='G' && $PROGRAM->kategor<PERSON>_Pengajian!='E' && $PROGRAM->kategori_Pengajian!='F'): ?>
	<?php $__currentLoopData = $syarat_umur; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratumur): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
		<li style="padding-left: .3em; margin-bottom:8px;">                           
			<?php if($loop->first): ?>
				<?php if($syaratumur->Operasi_Umur=='>'): ?>  Berumur <b><?php echo e($syaratumur->Umur1); ?> tahun <?php if($syaratumur->bulan1=='0'): ?> <?php else: ?> <?php echo e($syaratumur->bulan1); ?> bulan <?php endif; ?> keatas</b>.
				<?php elseif($syaratumur->Operasi_Umur=='<'): ?>  Berumur <b><?php echo e($syaratumur->Umur1); ?> tahun <?php if($syaratumur->bulan1=='0'): ?>  <?php else: ?> <?php echo e($syaratumur->bulan1); ?> bulan <?php endif; ?> kebawah</b>.
				<?php elseif($syaratumur->Operasi_Umur=='B'): ?>  Berumur <b><?php echo e($syaratumur->Umur1); ?> tahun <?php if($syaratumur->bulan1!='0'): ?> <?php echo e($syaratumur->bulan1); ?> bulan <?php endif; ?> hingga <?php echo e($syaratumur->Umur2); ?> tahun <?php if($syaratumur->bulan2!='0'): ?> <?php echo e($syaratumur->bulan2); ?> bulan <?php endif; ?> </b>.
				<?php endif; ?>
			<?php endif; ?>
		</li>
	<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>	

	<?php $__currentLoopData = $syarat_kahwin; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratkahwin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
		<li style="padding-left: .3em; margin-bottom:8px;">                           
			<?php if($syaratkahwin->Taraf_perkahwinan=='B'): ?> Taraf Perkahwinan : <b>Bujang</b>. <?php endif; ?>
		</li>
	<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>	

	<?php $__currentLoopData = $program; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

		<?php if($syarat_program->MEDSI=='Y'): ?>
			<li style="padding-left: .3em; margin-bottom:8px;">
				Melepasi tahap minimum ujian <b>Malaysian Educators Selection Inventory (MEdSI) yang ditetapkan oleh Universiti Awam</b>.    
			</li>                  
		<?php endif; ?>

		<?php if($syarat_program->TEMUDUGA=='Y'): ?>
			<li style="padding-left: .3em; margin-bottom:8px;">
				<b>Lulus ujian dan / atau temu duga</b> yang ditetapkan oleh Universiti Awam.    
			</li>                  
		<?php endif; ?>

		<?php if($syarat_program->OKU=='T'): ?>
			<li style="padding-left: .3em; margin-bottom:8px;">
				<b>TIDAK</b> mempunyai kurang upaya fizikal / anggota sehingga menyukarkan kerja-kerja amali.   
			</li>                   
		<?php endif; ?>

		<?php if($syarat_program->BUMI=='Y'): ?>
			<li style="padding-left: .3em; margin-bottom:8px;">
				Berketurunan Melayu, Anak Negeri Sabah, Anak Negeri Sarawak dan Orang Asli sahaja. 
			</li>                     
		<?php endif; ?>

	<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat_stpm/syarat_lain.blade.php ENDPATH**/ ?>