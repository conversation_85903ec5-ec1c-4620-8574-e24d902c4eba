

<?php $__currentLoopData = $j<PERSON><PERSON><PERSON><PERSON><PERSON>; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $jpengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

    <?php
    $syaratam_diploma  = DB::connection('emas')->table('syarat_am_diploma')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    ?>


    <?php if($jpengajian->KOD_PENGAJIAN=='P'): ?>
        <?php $__currentLoopData = $syaratam_diploma; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pendidikan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <span style="font-size:1rem !important; font-weight: normal; color: #000; line-height:1.8rem;"><?php echo $pendidikan->SYARAT; ?> </span>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>


<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/syarat_am_pendidikan.blade.php ENDPATH**/ ?>