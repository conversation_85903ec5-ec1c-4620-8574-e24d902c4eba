[2025-06-25 07:17:14] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 07:36:17] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 07:40:05] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:25:21] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:37:15] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:37:18] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:37:23] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:37:27] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:37:37] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:37:45] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:37:48] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:37:53] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:38:07] production.ERROR: Undefined variable: arr_BUMI {"exception":"[object] (ErrorException(code: 0): Undefined variable: arr_BUMI at C:\\xampp\\htdocs\\epanduan\\app\\Http\\Controllers\\ProgramPengajianController.php:143)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\app\\Http\\Controllers\\ProgramPengajianController.php(143): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8, 'Undefined varia...', 'C:\\\\xampp\\\\htdocs...', 143, Array)
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProgramPengajianController->index(Object(Illuminate\\Http\\Request), 'A')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('index', Array)
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProgramPengajianController), 'index')
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#6 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\epanduan\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\epanduan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 {main}
"} 
[2025-06-25 08:39:45] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:39:49] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:39:53] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:39:57] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:40:01] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:40:07] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:40:11] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-06-25 08:40:15] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-07-01 06:11:19] production.ERROR: SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 (SQL: select * from `sessions` where `id` = gXPnvPs8eDJ6zaxpdc7nOyJbhpAErN7xne3WDUjo limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.

 (SQL: select * from `sessions` where `id` = gXPnvPs8eDJ6zaxpdc7nOyJbhpAErN7xne3WDUjo limit 1) at C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2377): Illuminate\\Database\\Query\\Builder->first(Array)
#8 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(100): Illuminate\\Database\\Query\\Builder->find('gXPnvPs8eDJ6zax...')
#9 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(97): Illuminate\\Session\\DatabaseSessionHandler->read('gXPnvPs8eDJ6zax...')
#10 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(87): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(71): Illuminate\\Session\\Store->loadSession()
#12 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#13 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(263): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(148): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\epanduan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#22 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.

 at C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=10.2...', 'upu_apps', '2u@on9APPS%^&*', Array)
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=10.2...', 'upu_apps', '2u@on9APPS%^&*', Array)
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=10.2...', Array, Array)
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2377): Illuminate\\Database\\Query\\Builder->first(Array)
#18 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(100): Illuminate\\Database\\Query\\Builder->find('gXPnvPs8eDJ6zax...')
#19 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(97): Illuminate\\Session\\DatabaseSessionHandler->read('gXPnvPs8eDJ6zax...')
#20 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(87): Illuminate\\Session\\Store->readFromHandler()
#21 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(71): Illuminate\\Session\\Store->loadSession()
#22 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(147): Illuminate\\Session\\Store->start()
#23 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(263): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#24 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(148): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#26 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\epanduan\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}
"} 
[2025-07-01 06:56:46] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":null,"meritProgramMax":null,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":null,"meritProgramMax":null}} 
[2025-07-01 06:57:03] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":null,"meritProgramMax":null,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":null,"meritProgramMax":null}} 
[2025-07-01 07:01:22] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0","meritProgramMax":"100","all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"0","meritProgramMax":"100"}} 
[2025-07-01 07:01:49] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"50","meritProgramMax":"80","all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"50","meritProgramMax":"80"}} 
[2025-07-01 07:05:15] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"50.03","meritProgramMax":"80.01","all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"50.03","meritProgramMax":"80.01"}} 
[2025-07-01 07:05:21] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0","meritProgramMax":"100","all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"0","meritProgramMax":"100"}} 
[2025-07-01 07:10:09] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0","meritProgramMax":"50","all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"0","meritProgramMax":"50"}} 
[2025-07-01 07:10:14] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0","meritProgramMax":"50","all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"0","meritProgramMax":"50"}} 
[2025-07-01 07:11:15] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0.04","meritProgramMax":"50.0","all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"0.04","meritProgramMax":"50.0"}} 
[2025-07-01 07:26:08] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0.07","meritProgramMax":"90","meritProgramMin_type":"string","meritProgramMax_type":"string","meritProgramMin_empty":false,"meritProgramMax_empty":false,"meritProgramMin_null":false,"meritProgramMax_null":false,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"0.07","meritProgramMax":"90"}} 
[2025-07-01 07:26:08] production.INFO: Merit Filter Applied - SPM {"min":"0.07","max":"90","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0.07 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 90"} 
[2025-07-01 07:26:17] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"50","meritProgramMax":"90","meritProgramMin_type":"string","meritProgramMax_type":"string","meritProgramMin_empty":false,"meritProgramMax_empty":false,"meritProgramMin_null":false,"meritProgramMax_null":false,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"50","meritProgramMax":"90"}} 
[2025-07-01 07:26:17] production.INFO: Merit Filter Applied - SPM {"min":"50","max":"90","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 50 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 90"} 
[2025-07-01 07:26:28] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"60","meritProgramMax":"70","meritProgramMin_type":"string","meritProgramMax_type":"string","meritProgramMin_empty":false,"meritProgramMax_empty":false,"meritProgramMin_null":false,"meritProgramMax_null":false,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"meritProgramMin":"60","meritProgramMax":"70"}} 
[2025-07-01 07:26:29] production.INFO: Merit Filter Applied - SPM {"min":"60","max":"70","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 60 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 70"} 
[2025-07-01 07:28:47] production.INFO: Merit Filter Applied - SPM {"min":"60","max":"70","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 60 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 70"} 
[2025-07-01 07:29:02] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0","meritProgramMax":"100","meritProgramMin_type":"string","meritProgramMax_type":"string","meritProgramMin_empty":true,"meritProgramMax_empty":false,"meritProgramMin_null":false,"meritProgramMax_null":false,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"ModPengajian":["Y","T"],"pTemuduga":["Y"],"meritProgramMin":"0","meritProgramMax":"100"}} 
[2025-07-01 07:29:03] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
[2025-07-01 07:29:08] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0","meritProgramMax":"100","meritProgramMin_type":"string","meritProgramMax_type":"string","meritProgramMin_empty":true,"meritProgramMax_empty":false,"meritProgramMin_null":false,"meritProgramMax_null":false,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"pTVET":["Y"],"ModPengajian":["Y","T"],"pTemuduga":["Y"],"meritProgramMin":"0","meritProgramMax":"100"}} 
[2025-07-01 07:29:08] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
[2025-07-01 07:33:25] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
[2025-07-01 07:34:02] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
[2025-07-01 07:37:05] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
[2025-07-01 07:37:21] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
[2025-07-01 07:42:07] production.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php:650)
[stacktrace]
#0 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(701): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\xampp\\htdocs\\epanduan\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\epanduan\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\epanduan\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-07-01 07:42:12] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
[2025-07-01 07:42:25] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0","meritProgramMax":"100","meritProgramMin_type":"string","meritProgramMax_type":"string","meritProgramMin_empty":true,"meritProgramMax_empty":false,"meritProgramMin_null":false,"meritProgramMax_null":false,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"pBidang":["00"],"pTVET":["Y"],"ModPengajian":["Y","T"],"pTemuduga":["Y"],"meritProgramMin":"0","meritProgramMax":"100"}} 
[2025-07-01 07:42:25] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
[2025-07-01 07:42:32] production.INFO: Merit Filter Debug {"meritProgram":null,"meritProgramMin":"0","meritProgramMax":"100","meritProgramMin_type":"string","meritProgramMax_type":"string","meritProgramMin_empty":true,"meritProgramMax_empty":false,"meritProgramMin_null":false,"meritProgramMax_null":false,"all_inputs":{"_token":"E8ZrVj2EtYbpusdCH9C0OYeJIC3L1jxoFkDm5xRW","fuzzySearch":null,"searching":null,"pBidang":["04"],"meritProgramMin":"0","meritProgramMax":"100"}} 
[2025-07-01 07:42:32] production.INFO: Merit Filter Applied - SPM {"min":"0","max":"100","sql":"CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= 0 AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= 100"} 
