<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8" />
	<meta name="google-site-verification" content="knbeuhwyVSjnicym-QFZzp8uUS5kxLEj-FnUzAt3mcM" />
    <title>e-Panduan UPUOnline</title>
    <link rel="icon" href="{{ asset('/assets/images/_icons/_logos/favicon.png') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Bootstrap -->
    <link href="{{ asset('/assets/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />
    <!-- Icons -->
    <link href="{{ asset('/assets/css/materialdesignicons.min.css') }}" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="{{ asset('/assets/css/line.css') }}" />
    <!-- Magnific -->
    <link href="{{ asset('/assets/css/magnific-popup.css') }}" rel="stylesheet" type="text/css" />
    <!-- Slider -->
    <link rel="stylesheet" href="{{ asset('/assets/css/owl.carousel.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('/assets/css/owl.theme.default.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('/assets/css/slick.css') }}" />
    <link rel="stylesheet" href="{{ asset('/assets/css/slick-theme.css') }}" />
    <!-- Main Css -->
    <link href="{{ asset('/assets/css/style.css') }}" rel="stylesheet" type="text/css" id="theme-opt" />
    <link href="{{ asset('/assets/css/colors/default.css') }}" rel="stylesheet" id="color-opt" />
    <link rel="stylesheet" href="{{ asset('/assets/css/fonts.css') }}" />

       <!-- SELECT OPTION SEARCHING FORM CSS -->
   <link rel="stylesheet" type="text/css" href="{{ asset('css/select2.min.css') }}" >

</head>

<body>

    <style>
        @media (max-width: 575px){
            .d-sm1-block {
                display: block !important;
            }
        }

    .col_badge {
        width: 170px;
        border: 2px solid deeppink;
        border-color: deeppink !important;
        border-radius: 20px;
        padding-left: 10px;
        padding-right: 10px;
        padding-top: 4px;
        padding-bottom: 3px;
        font-weight: normal;
        text-align: left;
        /* font-size: 14px; */
    }

    .col_badge:hover  {
        box-shadow: none;
        border-color: none;
        background-color: deeppink !important;
    }

     .badge-deeppink  {
        background-color: deeppink !important;
        color: #ffffff !important;
        font-size: 13px !important;
    }

    .tooltip-inner {
        color: #000 !important;
        background-color: #F8FCC4 !important;
        max-width: 400px;
        text-align: left;
    }

    @media (min-width: 1200px) {
        .modal-llgg {
            max-width: 900px;
        }
    }
    </style>

	<!-- Google tag (gtag.js) -->
	<script async src="https://www.googletagmanager.com/gtag/js?id=G-WTPJ19WGDH"></script>
	<script>
	  window.dataLayer = window.dataLayer || [];
	  function gtag(){dataLayer.push(arguments);}
	  gtag('js', new Date());

	  gtag('config', 'G-WTPJ19WGDH');
	</script>



    <header id="topnav" class="defaultscroll sticky">
        <div class="row">
        <div class="container" style="max-width:70%">
            <div>
                <a class="logo" href="{{ url('/') }}">
                    <img src="{{ asset('/assets/images/_icons/_logos/LogoKPT-JPT.svg') }}" height="52" alt="" />
                </a>
            </div>
            <div class="menu-extras">
                <div class="menu-item">
                    <a class="navbar-toggle">
                        <div class="lines">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a>
                </div>
            </div>

            <div id="navigation">
                <!-- Navigation Menu-->
                <ul class="navigation-menu nav-right">
                    <li><a href="{{ url('/') }}">Laman Utama</a></li>
                    <li><a href="{{ url('senaraiAgensi') }}">Senarai IPTA & ILKA</a></li>
                    <li><a href="{{ url('carianNamaProgram') }}">Carian Program</a></li>
                    <li class="has-submenu">
                        <a>Syarat Kemasukan</a><span class="menu-arrow"></span>
                        <ul class="submenu">
                            <li><a href="<?= url('kategoriCalon?jenprog=spm') ?>" style="font-size: 13px">Lepasan
                                    SPM</a></li>
                            <li><a href="<?= url('kategoriCalon?jenprog=stpm') ?>" style="font-size: 13px">Lepasan STPM
                                    /
                                    Setaraf</a></li>
                        </ul>
                    </li>
                    <li class="has-submenu d-none d-lg-block d-xl-block" style="margin-left:25px">
                        <a>Kalkulator Merit</a><span class="menu-arrow"></span>
                        <ul class="submenu">
                            <li><a href="{{ url('kalkulatorMerit/spm') }}" style="font-size: 13px">Lepasan SPM</a></li>
                            <li><a href="{{ url('kalkulatorMerit/stpm') }}" style="font-size: 13px">Lepasan STPM / Setaraf</a></li>
                        </ul>
                    </li>
                    <li class="has-submenu d-none d-sm-block d-sm1-block d-md-block d-lg-none d-xl-none">
                        <a>Kalkulator Merit</a><span class="menu-arrow"></span>
                        <ul class="submenu">
                            <li><a href="{{ url('kalkulatorMerit/spm') }}" style="font-size: 13px">Lepasan SPM</a></li>
                            <li><a href="{{ url('kalkulatorMerit/stpm') }}" style="font-size: 13px">Lepasan STPM / Setaraf</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    </header>

    @yield('content')

    <!-- Footer Start -->
    <footer class="footer">
        <div class="container">
            <div class="row" style="justify-content: center">
                <div class="col-lg-4 col-12 mb-0 mb-md-4 pb-0 pb-md-2">
                    <a href="{{ url('/') }}" class="logo-footer">
                        <img src="{{ asset('/assets/images/_icons/_logos/LogoKPT-JPT-2.svg') }}" width="300"
                            alt="">
                    </a>
                    <p class="mt-4">
                        Bahagian Kemasukan Pelajar IPTA<br>Jabatan Pendidikan Tinggi,<br>Kementerian Pengajian
                        Tinggi<br>Aras 4, No. 2 Menara 2, Jalan P5/6<br>Presint 5 Pusat Pentadbiran
                        Kerajaan<br>Persekutuan 62200<br>Wilayah Persekutuan Putrajaya
                    </p>
                </div>

                <div class="col-lg-3 col-12 mt-4 mt-sm-0 pt-2 pt-sm-0">
                    <ul class="list-unstyled footer-list mt-4 sidebar-menu">
                        <li><a href="{{ url('/') }}" class="text-foot"><i class="mdi mdi-chevron-right mr-1"></i>
                                Laman
                                Utama</a></li>
                        <li><a href="{{ url('senaraiAgensi') }}" class="text-foot"><i
                                    class="mdi mdi-chevron-right mr-1"></i>
                                IPTA & ILKA</a></li>
                        <li><a href="{{ url('carianNamaProgram') }}" class="text-foot"><i
                                    class="mdi mdi-chevron-right mr-1"></i>
                                Carian Program</a></li>
                        <li class="have-children">
                            <a><i class="mdi mdi-chevron-right mr-1"></i>Syarat Kemasukan</a>
                            <ul class="list-unstyled">
                                <li>
                                    <a href="<?= url('kategoriCalon?jenprog=spm') ?>"
                                        style="font-size: 13px; color: #adb5bd"
                                        onclick="event.preventDefault(); window.location.href='<?= url('kategoriCalon?jenprog=spm') ?>'">
                                        Lepasan SPM
                                    </a>
                                </li>
                                <li><a href="<?= url('kategoriCalon?jenprog=stpm') ?>"
                                        onclick="event.preventDefault(); window.location.href='<?= url('kategoriCalon?jenprog=stpm') ?>'"
                                        style="font-size: 13px; color: #adb5bd">Lepasan
                                        STPM
                                        /
                                        Setaraf</a></li>
                            </ul>
                        </li>

                        <li class="have-children">
                            <a><i class="mdi mdi-chevron-right mr-1"></i>Kalkulator Merit</a>
                            <ul class="list-unstyled">
                                <li>
                                    <a href="{{ url('kalkulatorMerit/spm') }}"
                                        style="font-size: 13px; color: #adb5bd"
                                        onclick="event.preventDefault(); window.location.href='<?= url('kalkulatorMerit/spm') ?>'">
                                        Lepasan SPM
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url('kalkulatorMerit/stpm') }}"
                                        onclick="event.preventDefault(); window.location.href='<?= url('kalkulatorMerit/stpm') ?>'"
                                        style="font-size: 13px; color: #adb5bd">Lepasan STPM / Setaraf
                                    </a>
                                </li>
                            </ul>
                        </li>




                    </ul>
                </div>
            </div>
        </div>
    </footer>


<div class="chat-icon">
    <a href="#" onclick="window.open('https://upuchat.org/UPU_DaftarMasuk', 'UPUChat', 'width=800,height=600,top=100,left=100'); return false;">
        <div class="modern-image-wrapper">
            <!-- Using a placeholder image URL as fallback -->
            <img src="{{ asset('/assets/images/upuchat.png') }}" alt="Chat" onerror="this.src='https://via.placeholder.com/80'">
            <span class="notification-badge">1</span>
        </div>
    </a>
</div>

<style>
    /* ============================================= */
/* === PURE CSS TOOLTIP (MODERN & ELEGANT) === */
/* ============================================= */

/* The element that triggers the tooltip on hover */
.css-tooltip {
  position: relative; /* This is crucial for positioning the tooltip */
  cursor: pointer;
}

/* The main tooltip box, created with the ::after pseudo-element */
.css-tooltip::after {
  content: attr(data-tooltip); /* Pulls text from the data-tooltip attribute */
  position: absolute;
  bottom: 115%; /* Position above the badge */
  left: 50%;
  transform: translateX(-50%);

  /* Elegant Styling */
  background: #2c3e50; /* Dark, executive background */
  color: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap; /* Prevent text from wrapping */
  box-shadow: 0 4px 10px rgba(0,0,0,0.2);

  /* Hide it by default */
  opacity: 0;
  visibility: hidden;
  pointer-events: none; /* So the tooltip itself doesn't interfere with mouse events */

  /* Smooth fade-in animation */
  transition: opacity 0.2s ease-in-out, bottom 0.2s ease-in-out;
  z-index: 10;
}

/* The small arrow below the tooltip, created with the ::before pseudo-element */
.css-tooltip::before {
  content: '';
  position: absolute;
  bottom: 115%;
  left: 50%;
  transform: translateX(-50%) translateY(100%); /* Position it right below the box */

  /* Create the triangle shape */
  border: 5px solid transparent;
  border-top-color: #2c3e50;

  /* Hide it by default */
  opacity: 0;
  visibility: hidden;
  pointer-events: none;

  /* Smooth fade-in animation */
  transition: opacity 0.2s ease-in-out, bottom 0.2s ease-in-out;
  z-index: 10;
}

/* --- The Magic: Show the tooltip on hover --- */
.css-tooltip:hover::after,
.css-tooltip:hover::before {
  opacity: 1;
  visibility: visible;
  bottom: 125%; /* Move it up slightly on hover for a nice effect */
}






    .chat-icon {
        display: inline-block; /* Ensures proper sizing */
    }

    .modern-image-wrapper {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: white;
        padding: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
        display: flex; /* Helps with image alignment */
        align-items: center;
        justify-content: center;
    }

    .modern-image-wrapper img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        /* No color filters - keeping original colors */
    }

    .notification-badge {
        position: absolute;
        top: 0;
        right: 0;
        background: #ff4757;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        animation: ping 1.5s infinite;
    }

    @keyframes ping {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    .modern-image-wrapper:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }
</style>


    <!-- Modern Executive Card Styling -->
    <style>
/* =============================================
   New 'Executive Focus' Card CSS (Flicker-Free Design)
   ============================================= */

.focus-card {
    /* --- Custom Properties for easy theming --- */
    --focus-card-border-radius: 0.75rem; /* Matches Bootstrap's default 'card' */
    --focus-card-transition-speed: 0.3s;
    --focus-card-bg: white;

    /* --- Core Styles --- */
    /* This overrides Bootstrap's default border-radius if needed */
    border-radius: var(--focus-card-border-radius) !important;
    background-color: var(--focus-card-bg);

    /* Let the Bootstrap 'shadow' class provide the base shadow.
       We only define the transition behavior here. */
    transition: box-shadow var(--focus-card-transition-speed) ease-in-out;
}

/* --- Card Hover State --- */
.focus-card:hover {
    /* NO TRANSFORM: This is the key to avoiding the flicker. */

    /* On hover, give the card a deeper, more pronounced shadow to make it "lift" */
    box-shadow: 0 1rem 2.5rem rgba(33, 37, 41, 0.15) !important;
}


/* =============================================
   Styling for Content Inside the Card
   (Static styles without hover effects)
   ============================================= */

.focus-card .logo-container {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%; /* Circular logo container */
    background-color: #f8f9fa;
}

/* All hover effects for the logo, image, and button have been removed. */


        /* Executive Program Header */
        .executive-program-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1.3;
            margin-bottom: 0.75rem;
            letter-spacing: -0.02em;
        }

        .executive-university-name {
            font-size: 0.95rem;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .executive-interview-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Executive Badges */
        .executive-badges-container {
            margin-bottom: 1.5rem;
        }

        .executive-badge-feeder {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .executive-badge-stem {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .executive-badge-tvet {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
        }

        .executive-badge-competitive {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .executive-badge-btech {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(155, 89, 182, 0.3);
        }

        /* Executive Data Section */
        .executive-data-section {
            border-top: 1px solid #f1f3f4;
            padding-top: 1.5rem;
            margin-top: 1rem;
        }

        .executive-data-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem;
            height: 100%;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .executive-data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #007bff, #28a745);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .executive-data-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #dee2e6;
        }

        .executive-data-card:hover::before {
            opacity: 1;
        }

        .executive-data-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .executive-data-icon i {
            color: #6c757d;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .executive-data-card:hover .executive-data-icon {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .executive-data-card:hover .executive-data-icon i {
            color: white;
        }

        .executive-data-content {
            flex: 1;
        }

        .executive-data-label {
            display: block;
            font-size: 0.7rem;
            font-weight: 700;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.25rem;
        }

        .executive-data-value {
            display: block;
            font-size: 1rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1.2;
        }

        /* Executive Action Buttons */
        .executive-action-buttons-container {
            border-top: 1px solid #f1f3f4;
            padding-top: 1.5rem;
            margin-top: 1.5rem;
        }

        .executive-action-btn {
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0.75rem 1.25rem;
            border-radius: 25px;
            border-width: 2px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            min-height: 48px;
            position: relative;
            overflow: hidden;
        }

        .executive-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .executive-action-btn:hover::before {
            left: 100%;
        }

        .executive-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        .btn-outline-primary.executive-action-btn:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-color: #007bff;
            color: white;
        }

        .btn-outline-success.executive-action-btn:hover {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            border-color: #28a745;
            color: white;
        }

        .btn-outline-info.executive-action-btn:hover {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
            border-color: #17a2b8;
            color: white;
        }

        .btn-outline-warning.executive-action-btn:hover {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            border-color: #ffc107;
            color: #212529;
        }

        .btn-outline-secondary.executive-action-btn:hover {
            background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
            border-color: #6c757d;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .executive-program-title {
                font-size: 1.2rem;
            }

            .executive-university-name {
                font-size: 0.9rem;
            }

            .executive-action-btn {
                font-size: 0.8rem;
                padding: 0.6rem 1rem;
                min-height: 44px;
            }

            .executive-data-card {
                padding: 0.75rem;
            }

            .executive-data-icon {
                width: 40px;
                height: 40px;
                margin-right: 0.75rem;
            }

            .executive-logo-container {
                width: 60px;
                height: 60px;
            }
        }

        @media (max-width: 576px) {
            .executive-action-btn {
                font-size: 0.75rem;
                padding: 0.5rem 0.75rem;
                min-height: 40px;
            }

            .executive-program-title {
                font-size: 1.1rem;
            }

            .executive-data-card {
                padding: 0.6rem;
            }

            .executive-data-icon {
                width: 36px;
                height: 36px;
                margin-right: 0.6rem;
            }

            .executive-logo-container {
                width: 50px;
                height: 50px;
            }
        }

        /* Enhanced filter buttons */
        .filter-action-btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .filter-action-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced search input styling */
        .filter-search-input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .filter-search-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25), 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .input-group-text.search-icon {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #e9ecef;
            border-right: none;
            transition: all 0.3s ease;
        }

        .filter-search-input:focus + .search-icon,
        .input-group:focus-within .search-icon {
            border-color: #007bff;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .search-icon i {
            transition: all 0.3s ease;
        }

        .input-group:focus-within .search-icon i {
            color: #007bff !important;
            transform: scale(1.1);
        }

        /* MODERN FILTER CARD STYLES */
        .modern-filter-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .modern-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .modern-card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.8s ease;
        }

        .modern-card-header:hover::before {
            left: 100%;
        }

        .modern-card-title {
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .modern-card-body {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.95);
        }

        /* MODERN SEARCH INPUT */
        .modern-search-container {
            margin-bottom: 1.5rem;
        }

        .modern-input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .modern-search-input {
            width: 100%;
            padding: 12px 50px 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 0.95rem;
            background: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .modern-search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .modern-search-icon {
            position: absolute;
            right: 16px;
            color: #6c757d;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .modern-search-input:focus + .modern-search-icon {
            color: #667eea;
            transform: scale(1.1);
        }

        /* MODERN BUTTONS */
        .modern-button-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 1.5rem;
        }

        .modern-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 48px;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .modern-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .modern-btn:active {
            transform: translateY(0);
            transition: all 0.1s ease;
        }

        .modern-btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .modern-btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .modern-btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .modern-btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #d91a72 100%);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        }

        /* MODERN FILTER SECTIONS */
        .modern-filter-sections {
            margin-top: 1rem;
        }

        .modern-filter-section {
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .modern-filter-header {
            padding: 1rem 1.25rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #dee2e6;
        }

        .modern-filter-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .filter-title {
            font-weight: 600;
            color: #495057;
            font-size: 0.95rem;
        }

        .filter-chevron {
            transition: transform 0.3s ease;
            color: #6c757d;
        }

        .modern-filter-header[aria-expanded="true"] .filter-chevron {
            transform: rotate(180deg);
        }

        .modern-filter-content {
            padding: 1rem 1.25rem;
        }

        /* MODERN CHECKBOXES */
        .modern-checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .modern-checkbox-item {
            display: flex;
            align-items: center;
            position: relative;
        }

        .modern-checkbox {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            margin-right: 12px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
        }

        .modern-checkbox:checked {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            transform: scale(1.05);
        }

        .modern-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .modern-checkbox:hover {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modern-checkbox-label {
            font-size: 0.9rem;
            color: #495057;
            cursor: pointer;
            line-height: 1.4;
            transition: color 0.3s ease;
        }

        .modern-checkbox:checked + .modern-checkbox-label {
            color: #667eea;
            font-weight: 500;
        }

        /* MOBILE RESPONSIVE */
        @media (max-width: 991.98px) {
            .modern-card-body.collapse:not(.show) {
                display: none !important;
            }

            .modern-button-group {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .modern-btn {
                padding: 10px 16px;
                font-size: 0.85rem;
            }

            .modern-search-input {
                padding: 10px 40px 10px 14px;
                font-size: 0.9rem;
            }

            .modern-filter-header {
                padding: 0.75rem 1rem;
            }

            .modern-filter-content {
                padding: 0.75rem 1rem;
            }
        }

        @media (max-width: 575.98px) {
            .modern-card-header {
                padding: 1rem;
            }

            .modern-card-body {
                padding: 1rem;
            }

            .modern-card-title {
                font-size: 1rem;
            }

            .modern-checkbox-group {
                gap: 0.5rem;
            }
        }
    </style>


    <a href="#" class="btn btn-icon btn-primary back-to-top"><i data-feather="arrow-up" class="icons"></i></a>
    <!-- Back to top -->

    <!-- javascript -->
    <script src="{{ asset('/assets/js/jquery-3.5.1.min.js') }}"></script>
    <script src="{{ asset('/assets/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('/assets/js/jquery.easing.min.js') }}"></script>
    <script src="{{ asset('/assets/js/scrollspy.min.js') }}"></script>
    <script src="{{ asset('/assets/js/sidebar.js') }}"></script>
    <script src="{{ asset('/assets/js/font-awesome.js') }}"></script>
    <script src="{{ asset('/assets/js/paginator.js') }}"></script>

    <!-- Magnific -->
    <script src="{{ asset('/assets/js/jquery.magnific-popup.min.js') }}"></script>
    <script src="{{ asset('/assets/js/isotope.pkgd.min.js') }}"></script>
    <script src="{{ asset('/assets/js/portfolio.init.js') }}"></script>

    <!-- Icons -->
    <script src="{{ asset('/assets/js/feather.min.js') }}"></script>

    <!-- Main Js -->
    <script src="{{ asset('/assets/js/app.js') }}"></script>
    <script src="{{ asset('/assets/js/modal.js') }}"></script>

    <script type="text/javascript" src="{{ asset('js/select2.min.js') }}"></script>

    @if(Request::path()=='kalkulatorMerit/spm')
        @include('kalkulatorMerit.calc_spm_js')
    @endif

    @if(Request::path()=='kalkulatorMerit/stpm')

        <span id="stpm_page">
            @include('kalkulatorMerit.calc_stpm_js')
        </span>

        <span id="stam_page">
            @include('kalkulatorMerit.calc_stam_js')
        </span>

        <span id="diploma_page">
            @include('kalkulatorMerit.calc_diploma_js')
        </span>

    @endif

    <script>
        $(document).ready(function(){
            $("#stpm_page").show();
            $("#diploma_page").hide();
            $("#stam_page").hide();
        });
    </script>

    <script>
        $(document).ready(function(){
            $(".progControlSelect2").select2()
        });

    function kategori()
    {
        var katag = document.getElementById("KATEGORI");
        var katag = katag.value;

        if (katag=="stpm")
        {
            $("#stpm_page").show();
            $("#diploma_page").hide();
            $("#stam_page").hide();

            document.getElementById("MRKPNGKSTPM").value = parseFloat(Number("0.0")).toFixed(2);
            document.getElementById("MRKMERITSTPM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTPM').val('10.00').select2();
            document.getElementById("TOTALSTPM1").value = parseFloat(Number("0.0")).toFixed(2);


        }
        else if (katag=="stam")
        {
            $("#stpm_page").hide();
            $("#diploma_page").hide();
            $("#stam_page").show();

            document.getElementById("MRKPNGSTAM").selectedIndex='';
            document.getElementById("MRKMERITSTAM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTAM').val('10.00').select2();
            document.getElementById("TOTALSTAM1").value = parseFloat(Number("0.0")).toFixed(2);

        }
        else if (katag=="diploma")
        {
            $("#stpm_page").hide();
            $("#diploma_page").show();
            $("#stam_page").hide();

            document.getElementById("MRKPNGKDIPLOMA").value = parseFloat(Number("0.0")).toFixed(2);
            document.getElementById("TOTALDIPLOMA").value = parseFloat(Number("0")).toFixed(2);

        }
        else
        {
            $("#stpm_page").show();
            $("#diploma_page").hide();
            $("#stam_page").hide();

            document.getElementById("MRKPNGKSTPM").value = parseFloat(Number("0.0")).toFixed(2);
            document.getElementById("MRKMERITSTPM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTPM').val('10.00').select2();
            document.getElementById("TOTALSTPM1").value = parseFloat(Number("0.0")).toFixed(2);

        }
    }
    </script>

    <script>
        $(document).ready(function() {

            $('#carianKategori').on('change',function()
            {

                if($(this).data('options') == undefined)
                {
                    $(this).data('options', $('#kategoriProgram option').clone());
                }

                if($(this).data('options1') == undefined)
                {
                    $(this).data('options1', $('#carianIPTA option').clone());
                }

                    var katag1 = $(this).val();
                    // alert(katag1);
                    if (katag1=='spm')
                    {
                        var options1 = $(this).data('options').filter('[data-value=""]');
                        var options2 = $(this).data('options').filter('[data-value=' + katag1 + ']');
                        $('#kategoriProgram').html([options1,options2]).show();

                        var options3 = $(this).data('options1').filter('[data-value=""]');
                        var options4 = $(this).data('options1').filter('[data-value=' + katag1 + ']');
                        $('#carianIPTA').html([options3,options4]).show();

                        $('#carianKategori').on('change', function()
                        {
                            document.getElementById("kategoriProgram").value ="";
                            document.getElementById("carianIPTA").value ="";
                        });

                    }
                    else if (katag1=='stpm')
                    {
                        var options1 = $(this).data('options').filter('[data-value=""]');
                        var options2 = $(this).data('options').filter('[data-value=' + katag1 + ']');
                        $('#kategoriProgram').html([options1,options2]).show();

                        var options3 = $(this).data('options1').filter('[data-value2=""]');
                        var options4 = $(this).data('options1').filter('[data-value2=' + katag1 + ']');
                        $('#carianIPTA').html([options3,options4]).show();

                        $('#carianKategori').on('change', function()
                        {
                            document.getElementById("kategoriProgram").value ="";
                            document.getElementById("carianIPTA").value ="";
                        });
                    }
                    else
                    {
                        var options1 = $(this).data('options').filter('[data-value=""]');
                        document.getElementById("kategoriProgram").value ="";
                        $('#kategoriProgram').html(options1).show();

                        var options2 = $(this).data('options1').filter('[data-value=""]');
                        document.getElementById("carianIPTA").value ="";
                        $('#carianIPTA').html(options2).show();

                        $('#carianKategori').on('change', function()
                        {
                            document.getElementById("kategoriProgram").value ="";
                            document.getElementById("carianIPTA").value ="";
                        });
                    }
            });
        });
    </script>

    <script>
        $(document).ready(function() {

            $('#carianKategori').trigger('change',function()
            {

                if($(this).data('options') == undefined)
                {
                    $(this).data('options', $('#kategoriProgram option').clone());
                }

                if($(this).data('options1') == undefined)
                {
                    $(this).data('options1', $('#carianIPTA option').clone());
                }

                var katag1 = $(this).val();

                if (katag1=='spm')
                {
                    var options1 = $(this).data('options').filter('[data-value=""]');
                    var options2 = $(this).data('options').filter('[data-value=' + katag1 + ']');
                    $('#kategoriProgram').html([options1,options2]).show();

                    var options3 = $(this).data('options1').filter('[data-value=""]');
                    var options4 = $(this).data('options1').filter('[data-value=' + katag1 + ']');
                    $('#carianIPTA').html([options3,options4]).show();

                    $('#carianKategori').on('change', function()
                    {
                        document.getElementById("kategoriProgram").value ="";
                        document.getElementById("carianIPTA").value ="";
                    });
                }
                else if (katag1=='stpm')
                {
                    var options1 = $(this).data('options').filter('[data-value=""]');
                    var options2 = $(this).data('options').filter('[data-value=' + katag1 + ']');
                    $('#kategoriProgram').html([options1,options2]).show();

                    var options3 = $(this).data('options1').filter('[data-value2=""]');
                    var options4 = $(this).data('options1').filter('[data-value2=' + katag1 + ']');
                    $('#carianIPTA').html([options3,options4]).show();

                    $('#carianKategori').on('change', function()
                    {
                        document.getElementById("kategoriProgram").value ="";
                        document.getElementById("carianIPTA").value ="";
                    });
                }
                else
                {
                    var options1 = $(this).data('options').filter('[data-value=""]');
                    document.getElementById("kategoriProgram").value ="";
                    $('#kategoriProgram').html(options1).show();

                    var options2 = $(this).data('options1').filter('[data-value=""]');
                    document.getElementById("carianIPTA").value ="";
                    $('#carianIPTA').html(options2).show();

                    $('#carianKategori').on('change', function()
                    {
                        document.getElementById("kategoriProgram").value ="";
                        document.getElementById("carianIPTA").value ="";
                    });
                }
            });
        });
    </script>

	<script>
		$(document).ready(function(){
			$('[data-toggle="tooltip"]').tooltip();
		});
	</script>

</body>

</html>
