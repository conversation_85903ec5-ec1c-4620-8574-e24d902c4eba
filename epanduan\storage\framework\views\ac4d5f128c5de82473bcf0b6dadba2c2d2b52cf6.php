<?php $__currentLoopData = $SENARAI_PROGRAM; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $PROGRAM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php
        $sessionSesi = session()->get('sesi_semasa');
        include(app_path() . '/Http/Controllers/include_papar_stpm/jum_aliran.php');

        // KUMPULAN 0
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syarat_1119.php');


        // KUMPULAN 1
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syarat_1119.php');

        // // KUMPULAN 2
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syarat_1119.php');

        // // KUMPULAN 3
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syarat_1119.php');

        // // KUMPULAN 4
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syarat_1119.php');

        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_umur_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_umur.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/program_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/program.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_kahwin_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_kahwin.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_diploma.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_stpm_matrik_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_stpm_matrik.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_stam_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_stam.php');

    ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


  <?php if(isset($PROGRAM) && $PROGRAM->kategori_Pengajian != 'G' && $PROGRAM->kategori_Pengajian != 'E' && $PROGRAM->kategori_Pengajian != 'F'): ?>

    <ol style="padding-left: 2em; line-height: 1.5rem;" style="list-style-type:decimal;">

        <?php echo $__env->make('programPengajian.cetak_syarat_stpm.syarat_pngk_0', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('programPengajian.cetak_syarat_stpm.syarat_pngk', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        
        


        <?php if(count($syaratkhas_nn_stpm) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_stpm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_g1) > 0 ): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>



          





        <?php if(count($syaratkhas_f5) > 0): ?>
            <?php if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5) > 0) && !empty($syaratkhas_f5[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_f2) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_f4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_nn_spm) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_g2) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_g3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_f1) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_f3) == '1'): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syarat_muet) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k0.syarat_muet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        
        

        <?php if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) &&  count($syaratkhas_nn_stpm_1) > 0 && $syaratkhas_g1_1[0]->KODSUBJEK_1 == $syaratkhas_g1_2[0]->KODSUBJEK_1): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_nn_stpm_1) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_stpm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        
        <?php if(count($syaratkhas_f5_1) > 0): ?>
            <?php if((count($syaratkhas_f5_1) > 0 && count($syaratkhas_f5_2) > 0) && $syaratkhas_f5_1[0]->KODSUBJEK_1 == $syaratkhas_f5_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_ga_1) == 0): ?>



            <?php if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) &&  count($syaratkhas_nn_stpm_1) == 0 && $syaratkhas_g1_1[0]->KODSUBJEK_1 == $syaratkhas_g1_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>



           

            <?php if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && $syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1 && (count($syaratkhas_g1_1) == count($syaratkhas_g1_2)) && count($syaratkhas_g2_2) > 0): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && $syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1 && count($syaratkhas_g1_1) != count($syaratkhas_g1_2)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

           

            <?php if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) == 0) && !empty($syaratkhas_g1_1[0]->KODSUBJEK_1) && count($syaratkhas_nn_stpm_1) == 0): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
            

        <?php endif; ?>

        <?php if(count($syaratkhas_g1_1) == 0 && count($syaratkhas_ga_1) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_ga', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        

        
        <?php if(isset($syaratkhas_g1_1) && isset($syaratkhas_ga_1) &&
              count($syaratkhas_g1_1) > 0 && count($syaratkhas_ga_1) > 0): ?>
            <?php if(isset($syaratkhas_sk_1) && count($syaratkhas_sk_1) > 0): ?>
               <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_sk1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
         <?php endif; ?>


        <?php if(count($syaratkhas_f2_1) > 0): ?>

            <?php if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) > 0) && $syaratkhas_f2_1[0]->KUMPULAN == $syaratkhas_f2_2[0]->KUMPULAN): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) == 0) &&  !empty($syaratkhas_f2_1[0]->KUMPULAN)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

        <?php endif; ?>

        <?php if(count($syaratkhas_f4_1) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if((count($jum_aliran)=='2' && count($syaratkhas_f5_1) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f5_1) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f5_1) =='4')): ?>
            
        <?php endif; ?>


        <?php if(count($syaratkhas_nn_spm_1) > 0): ?>

            <?php if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && $syaratkhas_nn_spm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_spm_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) == 0) &&  !empty($syaratkhas_nn_spm_1[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) == 0) &&  !empty($syaratkhas_nn_spm_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED != $syaratkhas_nn_spm_2[0]->MINGRED)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

        <?php endif; ?>


        <?php if(count($syaratkhas_g2_1) > 0): ?>

            <?php if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 != $syaratkhas_g2_2[0]->KODSUBJEK_1): ?>
                
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1 && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) == 0 && count($syaratkhas_g2_1) == 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2_0', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) == 0 && count($syaratkhas_g2_1) != 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) > 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g2_1) == 0 && count($syaratkhas_g2_2) > 0) && !empty($syaratkhas_g2_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

        <?php endif; ?>



        <?php if(count($syaratkhas_g3_1) > 0): ?>
            <?php if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 != $syaratkhas_g3_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g3_1) == 0 && count($syaratkhas_g3_2) > 0) && !empty($syaratkhas_g3_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) ==  0) && !empty($syaratkhas_g3_1[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endif; ?>


        

        <?php if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) == 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if((count($jum_aliran)=='2' && count($syaratkhas_f3_1) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_1) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_1) =='4')): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3_0', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>




        <?php if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) && $syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND && $syarat_muet_1[0]->MUET2_Band != $syarat_muet_2[0]->MUET2_Band): ?>


            <?php if((count($syarat_1119_1) > 0 && count($syarat_1119_2) > 0) &&  $syarat_1119_1[0]->gred_1119 != $syarat_1119_2[0]->gred_1119 ): ?>
            <?php endif; ?>

            <?php if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) &&  ($syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND) && (empty($syarat_1119_1[0]->gred_1119) && empty($syarat_1119_2[0]->gred_1119))): ?>
            <?php endif; ?>



            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_muet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php endif; ?>

        


        



        
        


        <?php if(count($jum_aliran) > 1): ?>
            <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
        <?php endif; ?>


        <?php if(count($syaratkhas_nn_stpm_2) > 0): ?>

            <?php if((count($syaratkhas_nn_stpm_1) > 0 && count($syaratkhas_nn_stpm_2) > 0) && $syaratkhas_nn_stpm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_stpm_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_nn_stpm_1) > 0 && count($syaratkhas_nn_stpm_2) > 0) && $syaratkhas_nn_stpm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_stpm_2[0]->KODSUBJEK_1 && $syaratkhas_nn_stpm_1[0]->MINGRED != $syaratkhas_nn_stpm_2[0]->MINGRED): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_nn_stpm_1) == 0 && count($syaratkhas_nn_stpm_2) > 0) &&  !empty($syaratkhas_nn_stpm_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>


        <?php endif; ?>


        <?php if(count($syaratkhas_g1_2) > 0 && count($syaratkhas_ga_2) == 0): ?>

            <?php if((!empty($syaratkhas_g1_1[1]) && $syaratkhas_g1_1[1]->KODSUBJEK_1 != '') && (!empty($syaratkhas_g1_2[1]) && $syaratkhas_g1_2[1]->KODSUBJEK_1 != '')): ?>

                

                <?php if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0): ?>

                    <?php if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) || (count($syarat_muet_1) == 0 && count($syarat_muet_2) == 0)): ?>
                        <?php if($syarat_muet_1[0]->MUET1_BAND == $syarat_muet_2[0]->MUET1_BAND): ?>
                            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endif; ?>
                    <?php endif; ?>

                <?php endif; ?>

            <?php else: ?>

                <?php if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && ($syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1) && ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK)): ?>
                    <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>

                <?php if(count($syaratkhas_g1_1) == 0 && count($syaratkhas_g1_2) > 0): ?>
                    <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>





                <?php if(count($jum_aliran) == 2 && (count($syaratkhas_g1_1) > 0 && (count($syaratkhas_g1_2) > 0 && count($syaratkhas_g2_2) > 0))  && ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK) && (count($syaratkhas_g1_1)!=count($syaratkhas_g1_2))): ?>
                    <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>



                

                
                <?php
                // Reset the flag on every page load
                $term1_has_data = false;
                ?>

                
                <?php if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) &&
                ($syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1) &&
                ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK)): ?>

                 <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                 <?php
                $term1_has_data = true; // Only affects this page load
                ?>
                <?php endif; ?>

                
                <?php if(!$term1_has_data &&
                 (count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0 && count($syaratkhas_nn_spm_2) > 0) &&
                    count($syaratkhas_nn_stpm_1) == 0): ?>

                 <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <?php endif; ?>




            <?php endif; ?>


        <?php endif; ?>

        <?php if(count($syaratkhas_g1_2) == 0 && count($syaratkhas_ga_2) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_ga', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


        <?php endif; ?>

        <?php if(count($syaratkhas_g1_2) > 0 && count($syaratkhas_ga_2) > 0 ): ?>
            <?php if(count($syaratkhas_sk_2) > 0): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_sk1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

        <?php endif; ?>


        <?php if(count($syaratkhas_f2_2)): ?>
            <?php if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) > 0) && $syaratkhas_f2_1[0]->KUMPULAN != $syaratkhas_f2_2[0]->KUMPULAN): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) == 0) &&  !empty($syaratkhas_f2_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>


            <?php if(count($jum_aliran) == 2 && (count($syaratkhas_f2_1) == 0 && count($syaratkhas_f2_2) > 0) && count($syaratkhas_g2_2) > 0): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_f2_2) > 0 && count($syaratkhas_f2_3) > 0) && $syaratkhas_f2_2[0]->KUMPULAN == $syaratkhas_f2_3[0]->KUMPULAN): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>


        <?php endif; ?>


        <?php if(count($syaratkhas_f5_2) > 0): ?>
            <?php if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_2) > 0) && !empty($syaratkhas_f5_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

        <?php endif; ?>


        <?php if(count($syaratkhas_f4_2) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_nn_spm_2) > 0): ?>

            <?php if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && $syaratkhas_nn_spm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_spm_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_nn_spm_1) == 0 && count($syaratkhas_nn_spm_2) > 0) &&  !empty($syaratkhas_nn_spm_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED != $syaratkhas_nn_spm_2[0]->MINGRED)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>


        <?php endif; ?>


        
        <?php if(count($syaratkhas_g2_1) > 0): ?>

            <?php if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && ($syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1) && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

        <?php endif; ?>


        <?php if(count($syaratkhas_g2_2) > 0): ?>

            <?php if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 != $syaratkhas_g2_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g2_1) == 0 && count($syaratkhas_g2_2) > 0) && !empty($syaratkhas_g2_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1 && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>


        <?php endif; ?>

        <?php if(count($syaratkhas_g3_2) > 0): ?>
            <?php if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 == $syaratkhas_g3_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 != $syaratkhas_g3_2[0]->KODSUBJEK_1): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syaratkhas_g3_1) == 0 && count($syaratkhas_g3_2) > 0) && !empty($syaratkhas_g3_2[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_nn_spm_1) > 0): ?>
            <?php if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED == $syaratkhas_nn_spm_2[0]->MINGRED)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) > 0 && ($syaratkhas_f1_1[0]->MINGRED == $syaratkhas_f1_2[0]->MINGRED) && ($syaratkhas_f1_1[0]->JUMLAH_MIN_SUBJEK == $syaratkhas_f1_2[0]->JUMLAH_MIN_SUBJEK)): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) > 0 && ($syaratkhas_f1_1[0]->MINGRED == $syaratkhas_f1_2[0]->MINGRED) && ($syaratkhas_f1_1[0]->JUMLAH_MIN_SUBJEK != $syaratkhas_f1_2[0]->JUMLAH_MIN_SUBJEK)): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if((count($jum_aliran)=='2' && count($syaratkhas_f3_2) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_2) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_2) =='4')): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f3_0', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>



        <?php if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) && $syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND && $syarat_muet_1[0]->MUET2_Band != $syarat_muet_2[0]->MUET2_Band): ?>

            <?php if((count($syarat_1119_1) > 0 && count($syarat_1119_2) > 0) &&  $syarat_1119_1[0]->gred_1119 != $syarat_1119_2[0]->gred_1119 ): ?>

            <?php endif; ?>

            <?php if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) &&  ($syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND) && (empty($syarat_1119_1[0]->gred_1119) && empty($syarat_1119_2[0]->gred_1119))): ?>

            <?php endif; ?>

            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_muet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>



        
        

        <?php if(count($jum_aliran) > 2): ?>
            <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
        <?php endif; ?>


        <?php if(count($syaratkhas_nn_stpm_3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_stpm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_g1_3) > 0 && count($syaratkhas_ga_3) == 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php endif; ?>

        <?php if(count($syaratkhas_g1_3) == 0 && count($syaratkhas_ga_3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_ga', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


        <?php endif; ?>

        <?php if(count($syaratkhas_g1_3) > 0 && count($syaratkhas_ga_3) > 0 ): ?>
            <?php if(count($syaratkhas_sk_3) > 0): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_sk1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>


        <?php endif; ?>


        <?php if(count($syaratkhas_f5_3) > 0): ?>
            <?php if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_3) > 0) && !empty($syaratkhas_f5_3[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

        <?php endif; ?>

        <?php if(count($syaratkhas_f2_3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php endif; ?>

        <?php if(count($syaratkhas_f4_3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_nn_spm_3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_g2_3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php endif; ?>

        <?php if(count($syaratkhas_g3_3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_f1_3) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if((count($jum_aliran)=='2' && count($syaratkhas_f3_3) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_3) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_3) =='4')): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f3_0', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if((count($jum_aliran) > 1 && count($syaratkhas_f3_1) == '1')): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>



        
        

        <?php if(count($jum_aliran) > 3): ?>
            <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
        <?php endif; ?>


        <?php if(count($syaratkhas_nn_stpm_4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_stpm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_g1_4) > 0 && count($syaratkhas_ga_4) == 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


        <?php endif; ?>

        <?php if(count($syaratkhas_g1_4) == 0 && count($syaratkhas_ga_4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_ga', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php endif; ?>

        <?php if(count($syaratkhas_g1_4) > 0 && count($syaratkhas_ga_4) > 0 ): ?>
            <?php if(count($syaratkhas_sk_4) > 0): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_sk1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_f5_4) > 0): ?>
            <?php if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_4) > 0) && !empty($syaratkhas_f5_4[0]->KODSUBJEK_1)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f5', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
        <?php endif; ?>



        <?php if(count($syaratkhas_f2_4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>



        <?php endif; ?>

        <?php if(count($syaratkhas_f4_4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if((count($jum_aliran)=='2' && count($syaratkhas_f5_4) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f5_4) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f5_4) =='4')): ?>
            
        <?php endif; ?>


        <?php if(count($syaratkhas_nn_spm_4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_spm_nn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_g2_4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if(count($syaratkhas_g3_4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syaratkhas_f1_4) > 0): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if((count($jum_aliran)=='2' && count($syaratkhas_f3_4) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_4) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_4) =='4')): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f3_0', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if((count($jum_aliran) > 1 && count($syaratkhas_f4_1) == '1')): ?>
            <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>


        <?php if(count($syarat_muet_1) > 0 || count($syarat_muet_2) > 0 || count($syarat_muet_3) > 0 || count($syarat_muet_4) > 0): ?>

            <?php if(count($syarat_muet_1) > 0 && count($syarat_muet_2) == 0): ?>

                <?php if((count($syarat_1119_1) > 0 && count($syarat_1119_2) == 0)): ?>
                    <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_muet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>

                <?php if((count($syarat_1119_1) == 0 && count($syarat_1119_2) > 0)): ?>
                    <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_muet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>

                <?php if((count($syarat_1119_1) == 0 && count($syarat_1119_2) == 0)): ?>
                    <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k1.syarat_muet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>

            <?php endif; ?>

            <?php if((count($syarat_muet_1) == 0 && count($syarat_muet_2) > 0)): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_muet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>

            <?php if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0)): ?>
                <?php if($syarat_muet_1[0]->MUET1_BAND == $syarat_muet_2[0]->MUET1_BAND): ?>
                <?php echo $__env->make('programPengajian.cetak_syarat_stpm.k2.syarat_muet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>
            <?php endif; ?>


            

        <?php endif; ?>

        <?php echo $__env->make('programPengajian.cetak_syarat_stpm.syarat_lain', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('programPengajian.cetak_syarat_stpm.syarat_catatan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php echo $__env->make('programPengajian.cetak_syarat_stpm.syarat_lain_0', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('programPengajian.cetak_syarat_stpm.syarat_catatan_0', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    </ol>
<?php endif; ?>

<?php if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F'): ?>
    <?php $__currentLoopData = $syarat_diploma; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $diploma): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <span style="font-size:1rem !important; font-weight: normal; color: #000;">
            <?php echo $diploma->SYARAT; ?>

        </span>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>

<?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/syarat_stpm.blade.php ENDPATH**/ ?>