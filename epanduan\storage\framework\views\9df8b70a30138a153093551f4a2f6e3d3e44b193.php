
<?php $__currentLoopData = $syaratkhas_g1; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_g1): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if($loop->first): ?>
        <li style="padding-left: .3em; margin-bottom:8px;">
          Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_g1->MINGRED); ?></b> dalam <b><?php echo e($syarat_khas_g1->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syarat_khas_g1->JUMLAH_MIN_SUBJEK); ?>)</b> mata pelajaran di peringkat

          <?php if(!empty($syarat_khas_g1->MINGRED_2) || !empty($syarat_khas_g1->KET_JUMLAH_MIN_SUBJEK_2) || !empty($syarat_khas_g1->JUMLAH_MIN_SUBJEK_2)): ?>
          DAN <b><?php echo e($syarat_khas_g1->MINGRED_2 ?? '-'); ?></b>
          dalam <b><?php echo e($syarat_khas_g1->KET_JUMLAH_MIN_SUBJEK_2 ?? '-'); ?> (<?php echo e($syarat_khas_g1->JUMLAH_MIN_SUBJEK_2 ?? '-'); ?>)</b>
          mata pelajaran di peringkat
          <?php endif; ?>

            

             







            <b>
                <?php if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S'): ?> STPM
                <?php elseif($PROGRAM->kategori_Pengajian=='T'): ?> STAM
                <?php elseif($PROGRAM->kategori_Pengajian=='N'): ?> Matrikulasi / Asasi
                <?php elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J'): ?> Matrikulasi
                <?php else: ?> Asasi
                <?php endif; ?>
            </b> :
            <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
                <table cellpadding="2" width="100%">
                    <?php $__currentLoopData = $syaratkhas_g1; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_g1): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td style="vertical-align:top;">&#8226;</td>
                            <td style="vertical-align:top; width:95%"><?php echo e(ucwords(strtolower($syarat_khas_g1->KODSUBJEK_2))); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </table>
            </div>
        </li>
    <?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


<?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat_stpm/k0/syarat_khas_g1.blade.php ENDPATH**/ ?>