
<?php $__currentLoopData = $syaratkhas_nn_stpm; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_nn_stpm): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

    <?php if(substr($syarat_khas_nn_stpm->KODSUBJEK_1,0,1)!='K'): ?>
        <li style="padding-left: .3em; margin-bottom:8px;"> 
            Mendapat sekurang-kurangnya 
            
            <?php if($PROGRAM->kategori_Pengajian=='T'): ?>
                <?php $__currentLoopData = $codeset_tstam; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tahap_stam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($syarat_khas_nn_stpm[0]->MINGRED == $tahap_stam->kodthpstam): ?>
                        <b><?php echo e($tahap_stam->ketthpstam); ?></b>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
            Gred <b><?php echo e($syarat_khas_nn_stpm->MINGRED); ?></b>
            <?php endif; ?>
            
            dalam mata pelajaran
            
            <b>
                <?php echo e(ucwords(strtolower($syarat_khas_nn_stpm->KODSUBJEK_2))); ?>

            </b>
            di peringkat 
            
            <b>
                <?php if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S'): ?> STPM
                <?php elseif($PROGRAM->kategori_Pengajian=='T'): ?> STAM
                <?php elseif($PROGRAM->kategori_Pengajian=='N'): ?> Matrikulasi / Asasi
                <?php elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J'): ?> Matrikulasi
                <?php else: ?> Asasi
                <?php endif; ?>
            </b>.
            
        </li>
    <?php endif; ?>

    <?php if(substr($syarat_khas_nn_stpm->KODSUBJEK_1,0,1)=='K'): ?>
        <li style="padding-left: .3em; margin-bottom:8px;"> 
            Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_nn_stpm->MINGRED); ?></b> dalam <b><?php echo e($syarat_khas_nn_stpm->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syarat_khas_nn_stpm->JUMLAH_MIN_SUBJEK); ?>)</b> mata pelajaran di peringkat
            
            <b>
                <?php if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S'): ?> STPM
                <?php elseif($PROGRAM->kategori_Pengajian=='T'): ?> STAM
                <?php elseif($PROGRAM->kategori_Pengajian=='N'): ?> Matrikulasi / Asasi
                <?php elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J'): ?> Matrikulasi
                <?php else: ?> Asasi
                <?php endif; ?>
            </b> :
            <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
                <table cellpadding="2" width="100%">
                    <tr>
                        <td style="vertical-align:top;">&#8226;</td>
                        <td style="vertical-align:top; width:95%"><?php echo e(ucwords(strtolower($syarat_khas_nn_stpm->KODSUBJEK_2))); ?>}</td>
                    </tr>
                </table>
            </div>	
        </li>
    <?php endif; ?>

<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat_stpm/k0/syarat_khas_stpm_nn.blade.php ENDPATH**/ ?>