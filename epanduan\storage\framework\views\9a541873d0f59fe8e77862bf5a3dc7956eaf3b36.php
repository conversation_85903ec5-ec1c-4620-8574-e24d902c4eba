<?php $__env->startSection('content'); ?>
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h3 class="title"> PROGRAM PENGAJIAN</h3>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><PERSON><PERSON></a></li>
                                    <li class="breadcrumb-item"><a
                                            href="<?php echo e(url('kategoriCalon', [session('jenprog')])); ?>">Kategori
                                            <?php echo e(Request::route('kodkatag')); ?></a></li>
                                    <li class="breadcrumb-item"><a href="#">Program Pengajian</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">
            <form action="<?php echo e(url('ProgramPengajian/kategoriCalon/' . Request::route('kodkatag'))); ?>" method="post">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-lg-3 col-md-4 col-12">
                        <div class="card border-0 sidebar sticky-bar">
                            <div class="card-body p-0">
                                <!-- SEARCH -->
                                <div class="widget">
                                    <div id="search2" class="widget-search mb-0">
                                        <form class="searchform">
                                            <div>
                                                <input type="text" class="border rounded" id="fuzzySearch"
                                                    name="fuzzySearch" placeholder="Carian Program..."
                                                    value="<?php echo e(old('fuzzySearch')); ?>">
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="card border-0 shadow-sm mt-3">
                                    <div class="card-header bg-gradient-primary filter-card-header text-white d-flex justify-content-between align-items-center py-3">
                                        <h5 class="mb-0 text-white">
                                            <i class="fas fa-filter me-2"></i>Tapisan Carian
                                        </h5>
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-outline-success btn-sm executive-action-btn animated-btn filter-action-btn"
                                                    id="searching" name="searching"
                                                    style="font-size: 0.85rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(30, 126, 52, 0.1) 100%); border-color: #28a745; color: white;">
                                                <i class="fas fa-search mr-1"></i> Cari
                                            </button>
                                            <button type="submit" class="btn btn-outline-danger btn-sm executive-action-btn animated-btn filter-action-btn"
                                                    id="clearFiltering" name="clearFiltering"
                                                    style="font-size: 0.85rem; background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(176, 42, 55, 0.1) 100%); border-color: #dc3545; color: white;">
                                                <i class="fas fa-trash-alt mr-1"></i> Clear
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body p-3">

                                    <ul class="list-unstyled mt-4 mb-0 blog-categories sidebar-menu">
                                        <li class="have-children"><a href="#"><span class="fa fa-th-list"
                                                    style="padding-right: 7px;"></span>Bidang</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" name="pBidang[]" value="00"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('00', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck1">
                                                                <label class="custom-control-label"
                                                                    for="customCheck1">Program Dan Kelayakan Generik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="01" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('01', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck2">
                                                                <label class="custom-control-label"
                                                                    for="customCheck2">Pendidikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="02" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('02', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck4">
                                                                <label class="custom-control-label"
                                                                    for="customCheck4">Sastera
                                                                    Dan Kemanusiaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="03" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('03', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck5">
                                                                <label class="custom-control-label"
                                                                    for="customCheck5">Sains
                                                                    Sosial, Kewartawanan Dan Maklumat</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="04" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('04', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck6">
                                                                <label class="custom-control-label"
                                                                    for="customCheck6">Perniagaan, Pentadbiran Dan
                                                                    Perundangan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="05" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('05', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck7">
                                                                <label class="custom-control-label"
                                                                    for="customCheck7">Sains Semulajadi, Matematik Dan
                                                                    Statistik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="06" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('06', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck8">
                                                                <label class="custom-control-label"
                                                                    for="customCheck8">Teknologi Maklumat Dan
                                                                    Komunikasi</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="07" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('07', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck9">
                                                                <label class="custom-control-label"
                                                                    for="customCheck9">Kejuruteraan, Pembuatan Dan
                                                                    Pembinaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="08" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('08', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck10">
                                                                <label class="custom-control-label"
                                                                    for="customCheck10">Pertanian, Perhutanan, Perikanan
                                                                    Dan Vaterinar</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="09" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('09', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck11">
                                                                <label class="custom-control-label"
                                                                    for="customCheck11">Kesihatan Dan kebajikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="10" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('10', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck12">
                                                                <label class="custom-control-label"
                                                                    for="customCheck12">Perkhidmatan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-university"
                                                    style="padding-right: 7px;"></span>IPTA</a>
                                            <ul>
                                                <?php if(session()->get('jenprog') == 'spm'): ?>
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OM"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('OM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA30">
                                                                    <label class="custom-control-label" for="carianIPTA30">Kolej Mara</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FC"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('FC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA2">
                                                                    <label class="custom-control-label" for="carianIPTA2">Kolej Komuniti</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OP"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('OP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA31">
                                                                    <label class="custom-control-label" for="carianIPTA31">KPM</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FB"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('FB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA1">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA1">Politeknik</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                <?php endif; ?>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UY" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UY', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA27">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA27">UIAM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UE" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UE', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA13">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA13">UITM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UK" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UK', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA17">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA17">UKM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UM" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA19">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA19">UM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UJ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UJ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA16">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA16">UMPSA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UL" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UL', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA18">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA18">UMK</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UH" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UH', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA15">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA15">UMS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UG" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UG', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA14">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA14">UMT</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UR" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UR', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA22">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA22">UNIMAP</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UW" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UW', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA26">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA26">UNIMAS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UD" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UD', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA12">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA12">UNISZA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UP" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA20">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA20">UPM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UZ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UZ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA28">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA28">UPNM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UA" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UA', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA9">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA9">UPSI</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UQ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UQ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA21">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA21">USIM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="US" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('US', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA23">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA23">USM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UT" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UT', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA24">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA24">UTM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UC" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA11">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA11">UTeM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UB" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA10">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA10">UTHM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UU" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UU', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA25">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA25">UUM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <?php if(session()->get('jenprog') == 'spm'): ?>
                                            <li class="have-children"><a href="#"><span class="fa fa-code-fork"
                                                        style="padding-right: 7px;"></span>Peringkat Pengajian</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" name="peringkatPengajian[]"
                                                                        value="0"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('0', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck37">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck37">Asasi/Matrikulasi</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="1"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('1', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck38">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck38">Sijil-Kredit Graduan Min
                                                                        15</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="2"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('2', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck39">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck39">Sijil-Kredit Graduan Min
                                                                        30</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="3"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('3', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck40">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck40">Sijil-Kredit Graduan Min
                                                                        60</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="4"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('4', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck41">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck41">Diploma</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="5"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('5', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck42">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck42">Diploma Lanjutan</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="6"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('6', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck43">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck43">Sarjana Muda</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <li class="have-children"><a href="#"><span class="fa fa-cogs"
                                                    style="padding-right: 7px;"></span>Program TVET</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTVET[]"
                                                                    <?php if(!empty(session()->get('jTVET')) && in_array('Y', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck33">
                                                                <label class="custom-control-label"
                                                                    for="customCheck33">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTVET[]"
                                                                    <?php if(!empty(session()->get('jTVET')) && in_array('T', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck34">
                                                                <label class="custom-control-label"
                                                                    for="customCheck34">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-pie-chart"
                                                    style="padding-right: 7px;"></span>Mod Pengajian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y"
                                                                    name="ModPengajian[]"
                                                                    <?php if(!empty(session()->get('jMODpengajian')) && in_array('Y', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck35">
                                                                <label class="custom-control-label"
                                                                    for="customCheck35">2U2I/3U1I</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T"
                                                                    name="ModPengajian[]"
                                                                    <?php if(!empty(session()->get('jMODpengajian')) && in_array('T', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck36">
                                                                <label class="custom-control-label"
                                                                    for="customCheck36">Konvensional</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <?php if(session()->get('jenprog') == 'stpm'): ?>
                                            <li class="have-children"><a href="#"><span class="fa fa-certificate"
                                                        style="padding-right: 7px;"></span>Joint/Dual/Double Degree</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="Y"
                                                                        name="pDoubleDegree[]"
                                                                        <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('Y', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck44">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck44">Ya</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="T"
                                                                        name="pDoubleDegree[]"
                                                                        <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('T', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck45">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck45">Tidak</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        <li class="have-children"><a href="#"><span class="fa fa-hashtag"
                                                    style="padding-right: 7px;"></span>Bertemu duga / Ujian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTemuduga[]"
                                                                    <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('Y', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck31">
                                                                <label class="custom-control-label"
                                                                    for="customCheck31">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTemuduga[]"
                                                                    <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('T', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck32">
                                                                <label class="custom-control-label"
                                                                    for="customCheck32">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>

										<?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                        <li class="have-children"><a href="#"><span class="fa fa-line-chart"
                                                    style="padding-right: 7px;"></span>Minimum Purata Merit</a>
                                            <ul>
                                                <li>
                                                    <input type="range" min="0" max="100" value="0.00"
                                                        step="0.01" style="width: 60%" id="meritProgram"
                                                        name="meritProgram">
                                                    <b><label id="rangeDisplay" style="padding-left: 1rem"></label>%</b>
                                                </li>
                                            </ul>
                                        </li>
										<?php endif; ?>
                                        
                                    </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9 col-md-8 col-12 mt-5 pt-2 mt-sm-0 pt-sm-0">
                        <div class="row align-items-center">
                            <div class="col-lg-8 col-md-7">
                                <div class="section-title">
                                    <h5 class="mb-0">Paparan
                                        <?php echo e($SENARAI_PROGRAM->firstItem()); ?> - <?php echo e($SENARAI_PROGRAM->lastItem()); ?> daripada
                                        <?php echo e($SENARAI_PROGRAM->total()); ?> carian</h5>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <?php if(count($SENARAI_PROGRAM) != '0'): ?>
                                <?php $__currentLoopData = $SENARAI_PROGRAM; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $PROGRAM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-12 mt-3 pt-1">
                                        <div class="card shop-list border-0 shadow position-relative focus-card h-100">
                                            <div class="row align-items-center no-gutters">
                                                <div class="flex-grow-1 d-flex flex-column justify-content-between">
                                                    <div class="card-body content compact-card">
                                                        <div class="d-flex align-items-center gap-2 mt-1 mb-2">
                                                                            <div class="col-lg-4 col-md-6">
                                                                                <?php echo $__env->make('programPengajian.logoUA-ILKA', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                            </div>

                                                                            <div>
                                                                                <a href="#" class="text-decoration-none text-dark h5 fw-bold">
                                                                                    <?php echo e($PROGRAM->nama_Program); ?>

                                                                                    <?php if($PROGRAM->program_Temuduga == 'Y'): ?>
                                                                                        <span class="badge bg-warning text-dark ms-2">#</span>
                                                                                    <?php endif; ?>
                                                                                </a>

                                                                                <p class="text-muted small mb-0" style="margin-top: -0.5rem;">
                                                                                    <?php echo e(Str::title($PROGRAM->nama_IPTA)); ?>

                                                                                </p>
                                                                            </div>
                                                          </div>
                                                               <div >
                                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                                <?php if($PROGRAM->program_FEEDER == 'Y'): ?>
                                                                                    <span class="badge executive-badge-feeder rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program pembekal kepada program Ijazah Sarjana Muda di Universiti Awam berkenaan sahaja.">
                                                                                        <i class="fas fa-seedling me-1"></i>Program Perintis (Feeder)
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_STEM == 'Y'): ?>
                                                                                    <span class="badge executive-badge-stem rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program dalam bidang Sains, Teknologi, Kejuruteraan dan Matematik.">
                                                                                        <i class="fas fa-atom me-1"></i>STEM
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_TVET == 'Y'): ?>
                                                                                    <span class="badge executive-badge-tvet rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah.">
                                                                                        <i class="fas fa-tools me-1"></i>TVET
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                            <?php endif; ?>

                                                                            <?php if(session()->get('jenprog') == 'stpm'): ?>
                                                                                <?php if($PROGRAM->program_KOMPETITIF == 'Y'): ?>
                                                                                    <span class="badge executive-badge-competitive rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program pengajian popular dengan persaingan yang tinggi untuk kemasukan.">
                                                                                        <i class="fas fa-trophy me-1"></i>Kompetitif
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_BTECH == 'Y'): ?>
                                                                                    <span class="badge executive-badge-btech rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program Ijazah Sarjana Muda Teknologi Kejuruteraan yang diiktiraf oleh Lembaga Teknologis Malaysia (MBOT).">
                                                                                        <i class="fas fa-cogs me-1"></i>BTECH
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_TVET == 'Y'): ?>
                                                                                    <span class="badge executive-badge-tvet rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah.">
                                                                                        <i class="fas fa-tools me-1"></i>TVET
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                            <?php endif; ?>
                                                                        </div>

                                                        
                                                        

                                                         <!-- Executive Data Labels Section -->
                                                        <div class="executive-data-section mb-4">
                                                            <div class="row g-3">
                                                                <!-- Kod Program -->
                                                                <div class="col-md-4">
                                                                    <div class="executive-data-card">
                                                                        <div class="executive-data-icon">
                                                                            <i class="fas fa-tag"></i>
                                                                        </div>
                                                                        <div class="executive-data-content">
                                                                            <span class="executive-data-label">KOD PROGRAM</span>
                                                                            <span class="executive-data-value"><?php echo e($PROGRAM->kod_Program); ?></span>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Tahun -->
                                                                <div class="col-md-4">
                                                                    <div class="executive-data-card">
                                                                        <div class="executive-data-icon">
                                                                            <i class="far fa-calendar-alt"></i>
                                                                        </div>
                                                                        <div class="executive-data-content">
                                                                            <span class="executive-data-label">TAHUN</span>
                                                                            <span class="executive-data-value"><?php echo e(session()->get('tahun_semasa')); ?></span>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Merit -->
                                                                <?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                                                <div class="col-md-4">
                                                                    <div class="executive-data-card">
                                                                        <div class="executive-data-icon">
                                                                            <i class="fas fa-chart-line"></i>
                                                                        </div>
                                                                        <div class="executive-data-content">
                                                                            <span class="executive-data-label">PURATA MARKAH MERIT</span>
                                                                            <span class="executive-data-value">
                                                                                <?php $__currentLoopData = $MAKLUMAT_PENGAJIAN; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $maklumat_Pengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                    <?php if($maklumat_Pengajian->kod_Program == $PROGRAM->kod_Program): ?>
                                                                                        <?php if($maklumat_Pengajian->merit_Program==''): ?>
                                                                                            Tiada
                                                                                        <?php else: ?>
                                                                                            <?php echo $maklumat_Pengajian->merit_Program; ?>%
                                                                                            <i class="fas fa-info-circle text-primary ms-1"
                                                                                               data-bs-toggle="tooltip"
                                                                                               data-bs-placement="top"
                                                                                               title="Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata."></i>
                                                                                        <?php endif; ?>
                                                                                    <?php endif; ?>
                                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

                                                        <!-- Action Buttons Row -->
                                                        <div class="d-flex flex-wrap justify-content-center mb-2" style="gap: 4px;">
                                                            <!-- Syarat Program Button -->
                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                   id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                   class="btn btn-outline-primary btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                    <i class="fas fa-book mr-1"></i> Syarat
                                                                </a>
                                                            <?php elseif(session()->get('jenprog') == 'stpm'): ?>
                                                                <?php if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F'): ?>
                                                                    <a href="<?php echo e(url('ProgramPengajian/modalSyaratdiploma/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->jensetaraf . '/' . $PROGRAM->kod_Program)); ?>"
                                                                       id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                       class="btn btn-outline-primary btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                        <i class="fas fa-book mr-1"></i> Syarat
                                                                    </a>
                                                                <?php else: ?>
                                                                    <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                       id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                       class="btn btn-outline-primary btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                        <i class="fas fa-book mr-1"></i> Syarat
                                                                    </a>
                                                                <?php endif; ?>
                                                            <?php endif; ?>

                                                            <!-- Yuran Pengajian Button -->
                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#yuran-pengajian"
                                                               class="btn btn-outline-success btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                <i class="fas fa-dollar-sign mr-1"></i> Yuran
                                                            </a>

                                                            <!-- Kampus Button -->
                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#kampus__<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-info btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                <i class="fas fa-map-marked-alt mr-1"></i> Kampus
                                                            </a>

                                                            <!-- Laluan Kerjaya Button -->
                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#laluan-kerjaya_<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-warning btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                <i class="fas fa-briefcase mr-1"></i> Kerjaya
                                                            </a>

                                                            <!-- Bidang NEC Button (conditional) -->
                                                            <?php if(substr(Request::route('kodkatag'),0,1)=='G'): ?>
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#bidangNEC_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                   class="btn btn-outline-secondary btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                    <i class="fas fa-bullseye mr-1"></i> NEC
                                                                </a>
                                                            <?php endif; ?>

                                                            <!-- Maklumat Program Button -->
                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#maklumatProgram__<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-dark btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                <i class="fas fa-info-circle mr-1"></i> Info
                                                            </a>
                                                        </div>








                                                        <ul class="list-unstyled mb-0 flex-wrap">
                                                           
                                                           
                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                              
                                                                <div class="modal fade kelayakanMinimum_Modal"
                                                                    id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    tabindex="-1" role="dialog"
                                                                    aria-labelledby="syarat-program-title"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                        role="document">
                                                                        <div
                                                                            class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                            <button type="button"
                                                                                class="close float-right mr-2"
                                                                                data-dismiss="modal" aria-label="Close"
                                                                                style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                                <span aria-hidden="true">&times;</span>
                                                                            </button>
                                                                            <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                                <div class="text-left">
                                                                                    <h4 class="text-center"><b>Syarat
                                                                                            Program</b>
                                                                                    </h4>
                                                                                    <div class="container mt-100 mt-60">
                                                                                        <div class="row">
                                                                                            <div class="col-12">
                                                                                                <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                    id="pills-tab"
                                                                                                    role="tablist">
                                                                                                    <!--Syarat Am Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 active rounded"
                                                                                                            id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-am"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Am</h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>

                                                                                                    <!--Syarat Khas Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 rounded"
                                                                                                            id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-Khas"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Khas
                                                                                                                </h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>
                                                                                                </ul>

                                                                                                <div class="tab-content"
                                                                                                    id="pills-tabContent"
                                                                                                    style="padding-top: 2rem!important">
                                                                                                    <!--Paparan Syarat Am Tab-->
                                                                                                    <div class="card border-0 tab-pane fade show active"
                                                                                                        id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                        <?php echo $__env->make('programPengajian.syarat_am_spm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                        

                                                                                                    </div>

                                                                                                    <!--Paparan Syarat khas Tab-->
                                                                                                    <div class="card border-0 tab-pane fade"
                                                                                                        id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                        <div class="text-muted contentLoad"
                                                                                                            style="font-weight: bold">
                                                                                                            <div
                                                                                                                align="center">
                                                                                                                <div
                                                                                                                    class="loader-spinner text-center">
                                                                                                                </div>
                                                                                                                <h4>Sila
                                                                                                                    tunggu
                                                                                                                    sebentar...
                                                                                                                </h4>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>



                                                            <?php elseif(session()->get('jenprog') == 'stpm'): ?>



                                                           

                                                            <div class="modal fade kelayakanMinimum_Modal"
                                                                id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                tabindex="-1" role="dialog"
                                                                aria-labelledby="syarat-program-title"
                                                                aria-hidden="true">
                                                                <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                    role="document" >
                                                                    <div
                                                                        class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                        <button type="button"
                                                                            class="close float-right mr-2"
                                                                            data-dismiss="modal" aria-label="Close"
                                                                            style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                            <span aria-hidden="true">&times;</span>
                                                                        </button>
                                                                        <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                            <div class="text-left">
                                                                                <h4 class="text-center"><b>Syarat
                                                                                        Program</b>
                                                                                </h4>
                                                                                <div class="container mt-100 mt-60">
                                                                                    <div class="row">
                                                                                        <div class="col-12">
                                                                                            <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                id="pills-tab"
                                                                                                role="tablist">
                                                                                                <!--Syarat Am Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 active rounded"
                                                                                                        id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-am"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Am</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <li <?php echo e($PROGRAM->kod_Program == 'UM6143001' ? 'hidden' : null); ?>

                                                                                                    class="nav-item col-4">
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-pendidikan"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Pendidikan</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                                <?php endif; ?>


                                                                                                <!--Syarat Khas Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-Khas"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Khas
                                                                                                            </h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                            </ul>

                                                                                            <div class="tab-content"
                                                                                                id="pills-tabContent"
                                                                                                style="padding-top: 2rem!important">
                                                                                                <!--Paparan Syarat Am Tab-->
                                                                                                <div class="card border-0 tab-pane fade show active"
                                                                                                    id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php if($PROGRAM->kategori_Pengajian=='G'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_g', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='E'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_e', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='F'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_f', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php else: ?>
																										<?php echo $__env->make('programPengajian.syarat_am_stpm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php endif; ?>

                                                                                                </div>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php echo $__env->make('programPengajian.syarat_am_pendidikan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                </div>
                                                                                                <?php endif; ?>


                                                                                                <!--Paparan Syarat khas Tab-->
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                    <div class="text-muted contentLoad"
                                                                                                        style="font-weight: bold">
                                                                                                        <div
                                                                                                            align="center">
                                                                                                            <div
                                                                                                                class="loader-spinner text-center">
                                                                                                            </div>
                                                                                                            <h4>Sila
                                                                                                                tunggu
                                                                                                                sebentar...
                                                                                                            </h4>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>





                                                                
                                                            <?php endif; ?>
                                                            

                                                            
                                                            


                                                        </ul>
                                                    </div>
                                                </div>

                                                <!--end col-->
                                            </div>
                                            <!--end row-->
                                        </div>
                                        <!--end blog post-->
                                    </div>
                                    <?php echo $__env->make('programPengajian.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <?php echo $__env->make('pageLock.tiadaMaklumat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php endif; ?>

                            <!-- PAGINATION START -->
                            <div class="col-12 mt-4 pt-2">
                                <?php echo $SENARAI_PROGRAM->links('programPengajian.list-paginator'); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
    <script src="<?php echo e(asset('/assets/js/range-slider.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/index.blade.php ENDPATH**/ ?>