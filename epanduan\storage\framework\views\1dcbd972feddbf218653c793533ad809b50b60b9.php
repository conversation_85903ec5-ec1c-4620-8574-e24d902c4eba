<?php $__currentLoopData = $syaratkhas_g2; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_g2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
	<?php if($loop->first): ?>
		<li style="padding-left: .3em; margin-bottom:8px;">  
			Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_g2->MINGRED); ?></b> dalam <b><?php echo e($syarat_khas_g2->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syarat_khas_g2->JUMLAH_MIN_SUBJEK); ?>)</b> mata pelajaran di peringkat <b>SPM</b> :
			<div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
				<table cellpadding="2" width="100%">
					<?php $__currentLoopData = $syaratkhas_g2; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_g2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
						<tr>
							<td style="vertical-align:top;">&#8226;</td>
							<td style="vertical-align:top; width:95%"><?php echo e(ucwords(strtolower($syarat_khas_g2->KODSUBJEK_2))); ?></td>
						</tr>
					<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
				</table>
			</div>
		</li>
	<?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat_stpm/k0/syarat_khas_g2.blade.php ENDPATH**/ ?>